{"ast": null, "code": "import EventBooking from './components/EventBooking.vue';\nexport default {\n  name: 'App',\n  components: {\n    EventBooking\n  }\n};", "map": {"version": 3, "names": ["EventBooking", "name", "components"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <EventBooking />\n  </div>\n</template>\n\n<script>\nimport EventBooking from './components/EventBooking.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    EventBooking\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  background-color: #f8f9fa;\n}\n</style>\n\n"], "mappings": "AAOA,OAAAA,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}