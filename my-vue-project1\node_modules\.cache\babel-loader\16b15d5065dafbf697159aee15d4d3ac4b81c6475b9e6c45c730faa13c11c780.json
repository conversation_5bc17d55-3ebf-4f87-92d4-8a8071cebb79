{"ast": null, "code": "import EventBooking from './components/EventBooking.vue';\nimport HelloWorld from './components/EventBooking.vue';\nexport default {\n  name: 'App',\n  components: {\n    EventBooking\n  }\n};", "map": {"version": 3, "names": ["EventBooking", "HelloWorld", "name", "components"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <img alt=\"Vue logo\" src=\"./assets/logo.png\">\n    <HelloWorld msg=\"Welcome to Your Vue.js App\"/>\n  </div>\n</template>\n\n<script>\nimport EventBooking from './components/EventBooking.vue';\nimport HelloWorld from './components/EventBooking.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    EventBooking\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAQA,OAAAA,YAAA;AACA,OAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}