<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>📝 Task Manager App</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 40px auto;
      padding: 1rem;
      background-color: #f5f5f5;
    }
    input[type="text"] {
      padding: 10px;
      width: 70%;
      margin-right: 10px;
    }
    button {
      padding: 10px;
      cursor: pointer;
    }
    li {
      background: white;
      padding: 10px;
      margin: 5px 0;
      list-style: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .completed {
      text-decoration: line-through;
      color: gray;
    }
  </style>
</head>
<body>
  <div id="app">
    <h1>📝 Task Manager</h1>

    <input v-model="newTask" placeholder="Enter a task" @keyup.enter="addTask" />
    <button @click="addTask">Add Task</button>

    <ul>
      <li v-for="(task, index) in tasks" :key="index">
        <div>
          <input type="checkbox" v-model="task.completed" />
          <span :class="{ completed: task.completed }">{{ task.name }}</span>
        </div>
        <button @click="deleteTask(index)">❌</button>
      </li>
    </ul>
  </div>

  <script>
    const { createApp } = Vue;

    createApp({
      data() {
        return {
          newTask: '',
          tasks: []
        };
      },
      methods: {
        addTask() {
          if (this.newTask.trim()) {
            this.tasks.push({ name: this.newTask.trim(), completed: false });
            this.newTask = '';
          }
        },
        deleteTask(index) {
          this.tasks.splice(index, 1);
        }
      }
    }).mount('#app');
  </script>
</body>
</html>
