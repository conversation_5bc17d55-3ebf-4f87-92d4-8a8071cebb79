{"ast": null, "code": "import { buildApiUrl, API_ENDPOINTS } from '../config/api.js';\nexport default {\n  name: 'EventRegistrationForm',\n  props: {\n    show: {\n      type: Boolean,\n      default: false\n    },\n    event: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      isSubmitting: false,\n      successMessage: '',\n      form: {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    };\n  },\n  watch: {\n    show(newVal) {\n      if (newVal) {\n        this.resetForm();\n      }\n    }\n  },\n  methods: {\n    closeForm() {\n      this.$emit('close');\n    },\n    resetForm() {\n      this.form = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      };\n      this.successMessage = '';\n    },\n    validatePhone(phone) {\n      return phone.startsWith('60');\n    },\n    async submitRegistration() {\n      if (!this.validatePhone(this.form.contactNumber)) {\n        alert('Contact number must start with 60');\n        return;\n      }\n      this.isSubmitting = true;\n      try {\n        const registrationUrl = buildApiUrl(API_ENDPOINTS.EVENT_REGISTER(this.event.id));\n        console.log('Attempting registration to:', registrationUrl);\n        const response = await fetch(registrationUrl, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.form)\n        });\n        console.log('Response status:', response.status);\n        console.log('Response URL:', response.url);\n        if (!response.ok) {\n          if (response.url.includes('localhost') && !registrationUrl.includes('localhost')) {\n            throw new Error('Registration endpoint not available. The server may be redirecting to localhost, which suggests the endpoint does not exist.');\n          }\n          const errorData = await response.json().catch(() => ({\n            message: 'Unknown error'\n          }));\n          throw new Error(errorData.message || `Registration failed with status ${response.status}`);\n        }\n        await response.json();\n        this.successMessage = `Successfully registered for ${this.event.name}!`;\n\n        // Emit success event to parent\n        this.$emit('registration-success', this.event);\n\n        // Close form after short delay\n        setTimeout(() => {\n          this.closeForm();\n          this.successMessage = '';\n        }, 2000);\n      } catch (err) {\n        console.error('Registration error:', err);\n        if (err.message.includes('Failed to fetch')) {\n          alert('Registration failed: Unable to connect to the server. The registration endpoint may not be available yet.');\n        } else if (err.message.includes('CORS')) {\n          alert('Registration failed: Server configuration issue (CORS). Please contact support.');\n        } else {\n          alert(`Registration failed: ${err.message}`);\n        }\n      } finally {\n        this.isSubmitting = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["buildApiUrl", "API_ENDPOINTS", "name", "props", "show", "type", "Boolean", "default", "event", "Object", "required", "data", "isSubmitting", "successMessage", "form", "userFullName", "email", "contactNumber", "nric", "age", "country", "gender", "watch", "newVal", "resetForm", "methods", "closeForm", "$emit", "validatePhone", "phone", "startsWith", "submitRegistration", "alert", "registrationUrl", "EVENT_REGISTER", "id", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "status", "url", "ok", "includes", "Error", "errorData", "json", "catch", "message", "setTimeout", "err", "error"], "sources": ["src/components/EventRegistrationForm.vue"], "sourcesContent": ["<template>\n  <div v-if=\"show\" class=\"modal-overlay\" @click=\"closeForm\">\n    <div class=\"modal registration-modal\" @click.stop>\n      <div class=\"modal-header\">\n        <h3>Register for {{ event.name }}</h3>\n        <button class=\"close-btn\" @click=\"closeForm\">&times;</button>\n      </div>\n      \n      <div class=\"event-info\">\n        <p class=\"event-fee\">Registration Fee: <span class=\"fee-amount\">${{ event.fee }}</span></p>\n        <p class=\"remaining-slots\">\n          <i class=\"icon-users\"></i>\n          {{ event.remainingSlots }} slots remaining\n        </p>\n      </div>\n      \n      <form @submit.prevent=\"submitRegistration\" class=\"registration-form\">\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"userFullName\">\n              <i class=\"icon-user\"></i>\n              Full Name *\n            </label>\n            <input \n              type=\"text\" \n              id=\"userFullName\"\n              v-model=\"form.userFullName\" \n              placeholder=\"Enter your full name\"\n              required\n              class=\"form-input\"\n            />\n          </div>\n        </div>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"email\">\n              <i class=\"icon-mail\"></i>\n              Email Address *\n            </label>\n            <input \n              type=\"email\" \n              id=\"email\"\n              v-model=\"form.email\" \n              placeholder=\"<EMAIL>\"\n              required\n              class=\"form-input\"\n            />\n          </div>\n        </div>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"contactNumber\">\n              <i class=\"icon-phone\"></i>\n              Contact Number *\n            </label>\n            <input \n              type=\"tel\" \n              id=\"contactNumber\"\n              v-model=\"form.contactNumber\" \n              placeholder=\"60123456789\"\n              required\n              class=\"form-input\"\n            />\n            <small class=\"form-hint\">Must start with 60</small>\n          </div>\n        </div>\n        \n        <div class=\"form-row two-columns\">\n          <div class=\"form-group\">\n            <label for=\"nric\">\n              <i class=\"icon-id\"></i>\n              NRIC *\n            </label>\n            <input \n              type=\"text\" \n              id=\"nric\"\n              v-model=\"form.nric\" \n              placeholder=\"123456-78-9012\"\n              required\n              class=\"form-input\"\n            />\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"age\">\n              <i class=\"icon-calendar\"></i>\n              Age *\n            </label>\n            <input \n              type=\"number\" \n              id=\"age\"\n              v-model=\"form.age\" \n              placeholder=\"25\"\n              min=\"1\"\n              max=\"120\"\n              required\n              class=\"form-input\"\n            />\n          </div>\n        </div>\n        \n        <div class=\"form-row two-columns\">\n          <div class=\"form-group\">\n            <label for=\"country\">\n              <i class=\"icon-globe\"></i>\n              Country *\n            </label>\n            <input \n              type=\"text\" \n              id=\"country\"\n              v-model=\"form.country\" \n              placeholder=\"Malaysia\"\n              required\n              class=\"form-input\"\n            />\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"gender\">\n              <i class=\"icon-user-check\"></i>\n              Gender *\n            </label>\n            <select \n              id=\"gender\"\n              v-model=\"form.gender\" \n              required\n              class=\"form-input form-select\"\n            >\n              <option value=\"\">Select Gender</option>\n              <option value=\"M\">Male</option>\n              <option value=\"F\">Female</option>\n            </select>\n          </div>\n        </div>\n        \n        <div class=\"form-actions\">\n          <button type=\"button\" @click=\"closeForm\" class=\"btn btn-secondary\">\n            Cancel\n          </button>\n          <button type=\"submit\" :disabled=\"isSubmitting\" class=\"btn btn-primary\">\n            <span v-if=\"isSubmitting\" class=\"loading-spinner\"></span>\n            {{ isSubmitting ? 'Registering...' : 'Register Now' }}\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n  \n  <!-- Success Message -->\n  <div v-if=\"successMessage\" class=\"success-toast\">\n    <i class=\"icon-check\"></i>\n    {{ successMessage }}\n  </div>\n</template>\n\n<script>\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js'\n\nexport default {\n  name: 'EventRegistrationForm',\n  props: {\n    show: {\n      type: Boolean,\n      default: false\n    },\n    event: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      isSubmitting: false,\n      successMessage: '',\n      form: {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    }\n  },\n  watch: {\n    show(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    }\n  },\n  methods: {\n    closeForm() {\n      this.$emit('close')\n    },\n    \n    resetForm() {\n      this.form = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n      this.successMessage = ''\n    },\n    \n    validatePhone(phone) {\n      return phone.startsWith('60')\n    },\n    \n    async submitRegistration() {\n      if (!this.validatePhone(this.form.contactNumber)) {\n        alert('Contact number must start with 60')\n        return\n      }\n      \n      this.isSubmitting = true\n      try {\n        const registrationUrl = buildApiUrl(API_ENDPOINTS.EVENT_REGISTER(this.event.id))\n        console.log('Attempting registration to:', registrationUrl)\n        \n        const response = await fetch(registrationUrl, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.form)\n        })\n        \n        console.log('Response status:', response.status)\n        console.log('Response URL:', response.url)\n        \n        if (!response.ok) {\n          if (response.url.includes('localhost') && !registrationUrl.includes('localhost')) {\n            throw new Error('Registration endpoint not available. The server may be redirecting to localhost, which suggests the endpoint does not exist.')\n          }\n          \n          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))\n          throw new Error(errorData.message || `Registration failed with status ${response.status}`)\n        }\n        \n        await response.json()\n        this.successMessage = `Successfully registered for ${this.event.name}!`\n        \n        // Emit success event to parent\n        this.$emit('registration-success', this.event)\n        \n        // Close form after short delay\n        setTimeout(() => {\n          this.closeForm()\n          this.successMessage = ''\n        }, 2000)\n        \n      } catch (err) {\n        console.error('Registration error:', err)\n        \n        if (err.message.includes('Failed to fetch')) {\n          alert('Registration failed: Unable to connect to the server. The registration endpoint may not be available yet.')\n        } else if (err.message.includes('CORS')) {\n          alert('Registration failed: Server configuration issue (CORS). Please contact support.')\n        } else {\n          alert(`Registration failed: ${err.message}`)\n        }\n      } finally {\n        this.isSubmitting = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* Modal Overlay */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(4px);\n}\n\n/* Modal Container */\n.registration-modal {\n  background: white;\n  border-radius: 12px;\n  max-width: 600px;\n  width: 95%;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-20px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* Modal Header */\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 24px 0;\n  border-bottom: 1px solid #e5e7eb;\n  margin-bottom: 20px;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 28px;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 6px;\n  transition: all 0.2s;\n}\n\n.close-btn:hover {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n/* Event Info */\n.event-info {\n  padding: 0 24px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #f8fafc;\n  margin: 0 24px 20px;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.event-fee {\n  margin: 0;\n  color: #374151;\n  font-weight: 500;\n}\n\n.fee-amount {\n  color: #059669;\n  font-weight: 700;\n  font-size: 18px;\n}\n\n.remaining-slots {\n  margin: 0;\n  color: #6b7280;\n  font-size: 14px;\n}\n\n/* Form Styles */\n.registration-form {\n  padding: 0 24px 24px;\n}\n\n.form-row {\n  margin-bottom: 20px;\n}\n\n.form-row.two-columns {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 16px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group label {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #374151;\n  font-size: 14px;\n}\n\n.form-group label i {\n  margin-right: 8px;\n  width: 16px;\n  color: #6b7280;\n}\n\n.form-input {\n  padding: 12px 16px;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 16px;\n  transition: all 0.2s;\n  background: white;\n}\n\n.form-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-select {\n  cursor: pointer;\n}\n\n.form-hint {\n  margin-top: 4px;\n  color: #6b7280;\n  font-size: 12px;\n}\n\n/* Buttons */\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 32px;\n  padding-top: 20px;\n  border-top: 1px solid #e5e7eb;\n}\n\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 16px;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.btn-secondary:hover {\n  background: #e5e7eb;\n}\n\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2563eb;\n}\n\n.btn-primary:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* Loading Spinner */\n.loading-spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Success Toast */\n.success-toast {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: #059669;\n  color: white;\n  padding: 16px 20px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1001;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  animation: slideInRight 0.3s ease-out;\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Icons (using simple CSS shapes as placeholders) */\n.icon-user::before { content: \"👤\"; }\n.icon-mail::before { content: \"📧\"; }\n.icon-phone::before { content: \"📱\"; }\n.icon-id::before { content: \"🆔\"; }\n.icon-calendar::before { content: \"📅\"; }\n.icon-globe::before { content: \"🌍\"; }\n.icon-user-check::before { content: \"👥\"; }\n.icon-users::before { content: \"👥\"; }\n.icon-check::before { content: \"✅\"; }\n\n/* Responsive Design */\n@media (max-width: 640px) {\n  .registration-modal {\n    width: 100%;\n    height: 100%;\n    border-radius: 0;\n    max-height: 100vh;\n  }\n  \n  .form-row.two-columns {\n    grid-template-columns: 1fr;\n    gap: 12px;\n  }\n  \n  .event-info {\n    flex-direction: column;\n    gap: 8px;\n    text-align: center;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style>\n"], "mappings": "AA8JA,SAAAA,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,cAAA;MACAC,IAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,KAAA;IACAlB,KAAAmB,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,SAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,UAAA;MACA,KAAAC,KAAA;IACA;IAEAH,UAAA;MACA,KAAAV,IAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAAR,cAAA;IACA;IAEAe,cAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,UAAA;IACA;IAEA,MAAAC,mBAAA;MACA,UAAAH,aAAA,MAAAd,IAAA,CAAAG,aAAA;QACAe,KAAA;QACA;MACA;MAEA,KAAApB,YAAA;MACA;QACA,MAAAqB,eAAA,GAAAjC,WAAA,CAAAC,aAAA,CAAAiC,cAAA,MAAA1B,KAAA,CAAA2B,EAAA;QACAC,OAAA,CAAAC,GAAA,gCAAAJ,eAAA;QAEA,MAAAK,QAAA,SAAAC,KAAA,CAAAN,eAAA;UACAO,MAAA;UACAC,OAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,MAAA9B,IAAA;QACA;QAEAsB,OAAA,CAAAC,GAAA,qBAAAC,QAAA,CAAAO,MAAA;QACAT,OAAA,CAAAC,GAAA,kBAAAC,QAAA,CAAAQ,GAAA;QAEA,KAAAR,QAAA,CAAAS,EAAA;UACA,IAAAT,QAAA,CAAAQ,GAAA,CAAAE,QAAA,kBAAAf,eAAA,CAAAe,QAAA;YACA,UAAAC,KAAA;UACA;UAEA,MAAAC,SAAA,SAAAZ,QAAA,CAAAa,IAAA,GAAAC,KAAA;YAAAC,OAAA;UAAA;UACA,UAAAJ,KAAA,CAAAC,SAAA,CAAAG,OAAA,uCAAAf,QAAA,CAAAO,MAAA;QACA;QAEA,MAAAP,QAAA,CAAAa,IAAA;QACA,KAAAtC,cAAA,uCAAAL,KAAA,CAAAN,IAAA;;QAEA;QACA,KAAAyB,KAAA,8BAAAnB,KAAA;;QAEA;QACA8C,UAAA;UACA,KAAA5B,SAAA;UACA,KAAAb,cAAA;QACA;MAEA,SAAA0C,GAAA;QACAnB,OAAA,CAAAoB,KAAA,wBAAAD,GAAA;QAEA,IAAAA,GAAA,CAAAF,OAAA,CAAAL,QAAA;UACAhB,KAAA;QACA,WAAAuB,GAAA,CAAAF,OAAA,CAAAL,QAAA;UACAhB,KAAA;QACA;UACAA,KAAA,yBAAAuB,GAAA,CAAAF,OAAA;QACA;MACA;QACA,KAAAzC,YAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}