<template>
  <div class="calendar-container">
    <h2>Event Calendar</h2>
    <div class="calendar-header">
      <button @click="previousMonth">&lt;</button>
      <h3>{{ monthYear }}</h3>
      <button @click="nextMonth">&gt;</button>
    </div>
    
    <div class="calendar-grid">
      <div class="day-header" v-for="day in dayHeaders" :key="day">{{ day }}</div>
      
      <div 
        v-for="date in calendarDates" 
        :key="date.key"
        :class="['calendar-date', {
          'other-month': !date.isCurrentMonth,
          'has-event': date.hasEvent,
          'today': date.isToday
        }]"
        @click="date.hasEvent && selectDate(date)"
      >
        {{ date.day }}
        <div v-if="date.hasEvent" class="event-indicator"></div>
      </div>
    </div>

    <!-- Event details modal -->
    <div v-if="selectedDateEvents.length > 0" class="modal-overlay" @click="closeEventDetails">
      <div class="modal event-details-modal" @click.stop>
        <div class="modal-header">
          <h3>Events on {{ formatSelectedDate }}</h3>
          <button class="close-btn" @click="closeEventDetails">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <div class="modal-content">
          <div v-for="event in selectedDateEvents" :key="event.id" class="event-summary">
            <div class="event-header">
              <h4>{{ event.name }}</h4>
              <div class="event-meta">
                <span class="event-fee">${{ event.fee }}</span>
                <span class="event-slots" :class="{ 'slots-low': event.remainingSlots <= 5, 'slots-full': event.remainingSlots === 0 }">
                  {{ event.remainingSlots }} slots left
                </span>
              </div>
            </div>

            <p class="event-description">{{ event.description }}</p>

            <div class="event-actions">
              <button
                v-if="event.remainingSlots > 0"
                @click="openRegistrationForm(event)"
                class="register-btn"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M12.5 7.5C12.5 9.98528 10.4853 12 8 12C5.51472 12 3.5 9.98528 3.5 7.5C3.5 5.01472 5.51472 3 8 3C10.4853 3 12.5 5.01472 12.5 7.5ZM20 8V14M23 11H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Register Now
              </button>
              <div v-else class="full-event">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M15 9L9 15M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                Event is full
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Registration Form Component -->
    <EventRegistrationForm
      :show="showRegistrationForm"
      :event="selectedEvent || {}"
      @close="closeRegistrationForm"
      @registration-success="handleRegistrationSuccess"
    />
  </div>
</template>

<script>
import EventRegistrationForm from './EventRegistrationForm.vue'

export default {
  name: 'EventCalendar',
  components: {
    EventRegistrationForm
  },
  props: {
    events: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentDate: new Date(),
      selectedDate: null,
      selectedDateEvents: [],
      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      showRegistrationForm: false,
      selectedEvent: null
    }
  },
  computed: {
    monthYear() {
      return this.currentDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      })
    },
    
    formatSelectedDate() {
      return this.selectedDate ? this.selectedDate.toLocaleDateString() : ''
    },
    
    calendarDates() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()

      const firstDay = new Date(year, month, 1)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const dates = []
      const today = new Date()
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const hasEvent = this.hasEventOnDate(date)
        
        dates.push({
          key: date.toISOString(),
          day: date.getDate(),
          date: new Date(date),
          isCurrentMonth: date.getMonth() === month,
          hasEvent,
          isToday: this.isSameDay(date, today)
        })
      }
      
      return dates
    }
  },
  methods: {
    previousMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1)
    },
    
    nextMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1)
    },
    
    hasEventOnDate(date) {
      return this.events.some(event => 
        this.isSameDay(new Date(event.date), date)
      )
    },
    
    isSameDay(date1, date2) {
      return date1.getDate() === date2.getDate() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getFullYear() === date2.getFullYear()
    },
    
    selectDate(dateObj) {
      this.selectedDate = dateObj.date
      this.selectedDateEvents = this.events.filter(event =>
        this.isSameDay(new Date(event.date), dateObj.date)
      )
    },
    
    closeEventDetails() {
      this.selectedDate = null
      this.selectedDateEvents = []
    },

    openRegistrationForm(event) {
      this.selectedEvent = event
      this.showRegistrationForm = true
    },

    closeRegistrationForm() {
      this.showRegistrationForm = false
      this.selectedEvent = null
    },

    handleRegistrationSuccess(event) {
      // Update the event's remaining slots
      if (event && event.remainingSlots > 0) {
        event.remainingSlots -= 1
      }

      // Update the event in selectedDateEvents array to reflect the change
      const eventIndex = this.selectedDateEvents.findIndex(e => e.id === event.id)
      if (eventIndex !== -1) {
        this.selectedDateEvents[eventIndex].remainingSlots = event.remainingSlots
      }

      // Emit event to parent to refresh events if needed
      this.$emit('registration-success')

      // Close only the registration form, keep the event details modal open
      this.showRegistrationForm = false
      this.selectedEvent = null

      // Keep the event details modal open by not calling closeEventDetails()
    }
  }
}
</script>

<style scoped>
.calendar-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.calendar-header button {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #ddd;
}

.day-header {
  background: #34495e;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.calendar-date {
  background: white;
  padding: 10px;
  min-height: 60px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
}

.calendar-date.other-month {
  color: #bdc3c7;
  background: #f8f9fa;
}

.calendar-date.today {
  background: #e8f4fd;
  font-weight: bold;
}

.calendar-date.has-event {
  background: #d5f4e6;
  cursor: pointer;
}

.calendar-date.has-event:hover {
  background: #a8e6cf;
}

.event-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.event-details-modal {
  max-width: 600px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.close-btn svg {
  width: 20px;
  height: 20px;
}

/* Modal Content */
.modal-content {
  padding: 20px 24px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

/* Event Summary */
.event-summary {
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 0;
  transition: all 0.2s;
}

.event-summary:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.event-summary:hover {
  background: #f8fafc;
  margin: 0 -24px;
  padding-left: 24px;
  padding-right: 24px;
  border-radius: 8px;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.event-header h4 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  flex: 1;
}

.event-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.event-fee {
  background: #059669;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.event-slots {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 12px;
}

.event-slots.slots-low {
  background: #fef3c7;
  color: #d97706;
}

.event-slots.slots-full {
  background: #fee2e2;
  color: #dc2626;
}

.event-description {
  color: #6b7280;
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.event-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.register-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: center;
}

.register-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.register-btn svg {
  width: 16px;
  height: 16px;
}

.full-event {
  color: #dc2626;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #fee2e2;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

.full-event svg {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .modal {
    width: 95%;
    margin: 20px;
    max-height: 85vh;
  }

  .modal-header {
    padding: 20px 20px 0;
  }

  .modal-header h3 {
    font-size: 20px;
  }

  .modal-content {
    padding: 16px 20px 20px;
  }

  .event-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .event-meta {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .event-summary:hover {
    margin: 0 -20px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .event-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .register-btn {
    width: 100%;
    min-width: auto;
  }

  .full-event {
    justify-content: center;
  }
}



.event-fee-modal {
  color: #e67e22;
  font-weight: bold;
  font-size: 16px;
  margin: 10px 0 20px 0;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #27ae60;
  color: white;
  padding: 15px 20px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
