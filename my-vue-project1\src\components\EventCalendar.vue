<template>
  <div class="calendar-container">
    <h2>Event Calendar</h2>
    <div class="calendar-header">
      <button @click="previousMonth">&lt;</button>
      <h3>{{ monthYear }}</h3>
      <button @click="nextMonth">&gt;</button>
    </div>
    
    <div class="calendar-grid">
      <div class="day-header" v-for="day in dayHeaders" :key="day">{{ day }}</div>
      
      <div 
        v-for="date in calendarDates" 
        :key="date.key"
        :class="['calendar-date', {
          'other-month': !date.isCurrentMonth,
          'has-event': date.hasEvent,
          'today': date.isToday
        }]"
        @click="date.hasEvent && selectDate(date)"
      >
        {{ date.day }}
        <div v-if="date.hasEvent" class="event-indicator"></div>
      </div>
    </div>

    <!-- Event details modal -->
    <div v-if="selectedDateEvents.length > 0" class="modal-overlay" @click="closeEventDetails">
      <div class="modal" @click.stop>
        <h3>Events on {{ formatSelectedDate }}</h3>
        <div v-for="event in selectedDateEvents" :key="event.id" class="event-summary">
          <h4>{{ event.name }}</h4>
          <p>{{ event.description }}</p>
          <p><strong>Fee:</strong> ${{ event.fee }}</p>
          <p><strong>Remaining slots:</strong> {{ event.remainingSlots }}</p>
          <button
            v-if="event.remainingSlots > 0"
            @click="openRegistrationForm(event)"
            class="register-btn"
          >
            Register for Event
          </button>
          <p v-else class="full-event">Event is full</p>
        </div>
        <button @click="closeEventDetails">Close</button>
      </div>
    </div>

    <!-- Registration Form Component -->
    <EventRegistrationForm
      :show="showRegistrationForm"
      :event="selectedEvent || {}"
      @close="closeRegistrationForm"
      @registration-success="handleRegistrationSuccess"
    />
  </div>
</template>

<script>
import EventRegistrationForm from './EventRegistrationForm.vue'

export default {
  name: 'EventCalendar',
  components: {
    EventRegistrationForm
  },
  props: {
    events: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentDate: new Date(),
      selectedDate: null,
      selectedDateEvents: [],
      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      showRegistrationForm: false,
      selectedEvent: null
    }
  },
  computed: {
    monthYear() {
      return this.currentDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      })
    },
    
    formatSelectedDate() {
      return this.selectedDate ? this.selectedDate.toLocaleDateString() : ''
    },
    
    calendarDates() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()

      const firstDay = new Date(year, month, 1)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const dates = []
      const today = new Date()
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const hasEvent = this.hasEventOnDate(date)
        
        dates.push({
          key: date.toISOString(),
          day: date.getDate(),
          date: new Date(date),
          isCurrentMonth: date.getMonth() === month,
          hasEvent,
          isToday: this.isSameDay(date, today)
        })
      }
      
      return dates
    }
  },
  methods: {
    previousMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1)
    },
    
    nextMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1)
    },
    
    hasEventOnDate(date) {
      return this.events.some(event => 
        this.isSameDay(new Date(event.date), date)
      )
    },
    
    isSameDay(date1, date2) {
      return date1.getDate() === date2.getDate() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getFullYear() === date2.getFullYear()
    },
    
    selectDate(dateObj) {
      this.selectedDate = dateObj.date
      this.selectedDateEvents = this.events.filter(event =>
        this.isSameDay(new Date(event.date), dateObj.date)
      )
    },
    
    closeEventDetails() {
      this.selectedDate = null
      this.selectedDateEvents = []
    },

    openRegistrationForm(event) {
      this.selectedEvent = event
      this.showRegistrationForm = true
    },

    closeRegistrationForm() {
      this.showRegistrationForm = false
      this.selectedEvent = null
    },

    handleRegistrationSuccess(event) {
      // Update the event's remaining slots
      if (event && event.remainingSlots > 0) {
        event.remainingSlots -= 1
      }

      // Emit event to parent to refresh events if needed
      this.$emit('registration-success')

      // Close the registration form
      this.closeRegistrationForm()
    }
  }
}
</script>

<style scoped>
.calendar-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.calendar-header button {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #ddd;
}

.day-header {
  background: #34495e;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.calendar-date {
  background: white;
  padding: 10px;
  min-height: 60px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
}

.calendar-date.other-month {
  color: #bdc3c7;
  background: #f8f9fa;
}

.calendar-date.today {
  background: #e8f4fd;
  font-weight: bold;
}

.calendar-date.has-event {
  background: #d5f4e6;
  cursor: pointer;
}

.calendar-date.has-event:hover {
  background: #a8e6cf;
}

.event-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  background: #e74c3c;
  border-radius: 50%;
}

.event-summary {
  border-bottom: 1px solid #eee;
  padding: 15px 0;
}

.event-summary:last-child {
  border-bottom: none;
}

.register-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.register-btn:hover {
  background: #219a52;
}

.full-event {
  color: #e74c3c;
  font-weight: bold;
  margin-top: 10px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.registration-modal {
  max-width: 600px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-actions button[type="button"] {
  background: #95a5a6;
  color: white;
}

.form-actions button[type="submit"] {
  background: #3498db;
  color: white;
}

.form-actions button[type="submit"]:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.form-actions button:hover:not(:disabled) {
  opacity: 0.9;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #27ae60;
  color: white;
  padding: 15px 20px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1001;
}

.event-fee-modal {
  color: #e67e22;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 20px;
  text-align: center;
}

.event-fee-modal {
  color: #e67e22;
  font-weight: bold;
  font-size: 16px;
  margin: 10px 0 20px 0;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #27ae60;
  color: white;
  padding: 15px 20px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
