{"ast": null, "code": "// API Configuration\nexport const API_CONFIG = {\n  BASE_URL: 'https://quiz.vilor.com',\n  ENDPOINTS: {\n    EVENTS: {\n      LISTING: '/api/events/listing',\n      REGISTER: eventId => `/api/events/${eventId}/register`\n    }\n  }\n};\n\n// Helper function to build full API URLs\nexport const buildApiUrl = endpoint => {\n  return `${API_CONFIG.BASE_URL}${endpoint}`;\n};\n\n// API service functions\nexport const apiService = {\n  // Get events listing\n  getEvents: () => {\n    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.LISTING));\n  },\n  // Register for an event\n  registerForEvent: (eventId, registrationData) => {\n    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.REGISTER(eventId)), {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(registrationData)\n    });\n  }\n};", "map": {"version": 3, "names": ["API_CONFIG", "BASE_URL", "ENDPOINTS", "EVENTS", "LISTING", "REGISTER", "eventId", "buildApiUrl", "endpoint", "apiService", "getEvents", "fetch", "registerForEvent", "registrationData", "method", "headers", "body", "JSON", "stringify"], "sources": ["D:/sem6/myself/vue/my-vue-project1/src/config/api.js"], "sourcesContent": ["// API Configuration\nexport const API_CONFIG = {\n  BASE_URL: 'https://quiz.vilor.com',\n  ENDPOINTS: {\n    EVENTS: {\n      LISTING: '/api/events/listing',\n      REGISTER: (eventId) => `/api/events/${eventId}/register`\n    }\n  }\n}\n\n// Helper function to build full API URLs\nexport const buildApiUrl = (endpoint) => {\n  return `${API_CONFIG.BASE_URL}${endpoint}`\n}\n\n// API service functions\nexport const apiService = {\n  // Get events listing\n  getEvents: () => {\n    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.LISTING))\n  },\n  \n  // Register for an event\n  registerForEvent: (eventId, registrationData) => {\n    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.REGISTER(eventId)), {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(registrationData)\n    })\n  }\n}\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,UAAU,GAAG;EACxBC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE;IACTC,MAAM,EAAE;MACNC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAGC,OAAO,IAAK,eAAeA,OAAO;IAC/C;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EACvC,OAAO,GAAGR,UAAU,CAACC,QAAQ,GAAGO,QAAQ,EAAE;AAC5C,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,SAAS,EAAEA,CAAA,KAAM;IACf,OAAOC,KAAK,CAACJ,WAAW,CAACP,UAAU,CAACE,SAAS,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC;EAChE,CAAC;EAED;EACAQ,gBAAgB,EAAEA,CAACN,OAAO,EAAEO,gBAAgB,KAAK;IAC/C,OAAOF,KAAK,CAACJ,WAAW,CAACP,UAAU,CAACE,SAAS,CAACC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,CAAC,EAAE;MACvEQ,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,gBAAgB;IACvC,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}