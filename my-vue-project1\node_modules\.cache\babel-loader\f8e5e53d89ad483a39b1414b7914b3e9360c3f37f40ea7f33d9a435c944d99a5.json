{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport EventRegistrationForm from './EventRegistrationForm.vue';\nexport default {\n  name: 'EventCalendar',\n  components: {\n    EventRegistrationForm\n  },\n  props: {\n    events: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      currentDate: new Date(),\n      selectedDate: null,\n      selectedDateEvents: [],\n      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n      showRegistrationForm: false,\n      selectedEvent: null\n    };\n  },\n  computed: {\n    monthYear() {\n      return this.currentDate.toLocaleDateString('en-US', {\n        month: 'long',\n        year: 'numeric'\n      });\n    },\n    formatSelectedDate() {\n      return this.selectedDate ? this.selectedDate.toLocaleDateString() : '';\n    },\n    calendarDates() {\n      const year = this.currentDate.getFullYear();\n      const month = this.currentDate.getMonth();\n      const firstDay = new Date(year, month, 1);\n      const startDate = new Date(firstDay);\n      startDate.setDate(startDate.getDate() - firstDay.getDay());\n      const dates = [];\n      const today = new Date();\n      for (let i = 0; i < 42; i++) {\n        const date = new Date(startDate);\n        date.setDate(startDate.getDate() + i);\n        const hasEvent = this.hasEventOnDate(date);\n        dates.push({\n          key: date.toISOString(),\n          day: date.getDate(),\n          date: new Date(date),\n          isCurrentMonth: date.getMonth() === month,\n          hasEvent,\n          isToday: this.isSameDay(date, today)\n        });\n      }\n      return dates;\n    }\n  },\n  methods: {\n    previousMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);\n    },\n    nextMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);\n    },\n    hasEventOnDate(date) {\n      return this.events.some(event => this.isSameDay(new Date(event.date), date));\n    },\n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\n    },\n    selectDate(dateObj) {\n      this.selectedDate = dateObj.date;\n      this.selectedDateEvents = this.events.filter(event => this.isSameDay(new Date(event.date), dateObj.date));\n    },\n    closeEventDetails() {\n      this.selectedDate = null;\n      this.selectedDateEvents = [];\n    },\n    openRegistrationForm(event) {\n      this.selectedEvent = event;\n      this.showRegistrationForm = true;\n    },\n    closeRegistrationForm() {\n      this.showRegistrationForm = false;\n      this.selectedEvent = null;\n    },\n    handleRegistrationSuccess(event) {\n      // Update the event's remaining slots\n      if (event && event.remainingSlots > 0) {\n        event.remainingSlots -= 1;\n      }\n\n      // Emit event to parent to refresh events if needed\n      this.$emit('registration-success');\n\n      // Close the registration form\n      this.closeRegistrationForm();\n    }\n  }\n};", "map": {"version": 3, "names": ["EventRegistrationForm", "name", "components", "props", "events", "type", "Array", "default", "data", "currentDate", "Date", "selectedDate", "selectedDateEvents", "dayHeaders", "showRegistrationForm", "selectedEvent", "computed", "monthYear", "toLocaleDateString", "month", "year", "formatSelectedDate", "calendarDates", "getFullYear", "getMonth", "firstDay", "startDate", "setDate", "getDate", "getDay", "dates", "today", "i", "date", "hasEvent", "hasEventOnDate", "push", "key", "toISOString", "day", "isCurrentMonth", "isToday", "isSameDay", "methods", "previousMonth", "nextMonth", "some", "event", "date1", "date2", "selectDate", "date<PERSON><PERSON>j", "filter", "closeEventDetails", "openRegistrationForm", "closeRegistrationForm", "handleRegistrationSuccess", "remainingSlots", "$emit"], "sources": ["src/components/EventCalendar.vue"], "sourcesContent": ["<template>\n  <div class=\"calendar-container\">\n    <h2>Event Calendar</h2>\n    <div class=\"calendar-header\">\n      <button @click=\"previousMonth\">&lt;</button>\n      <h3>{{ monthYear }}</h3>\n      <button @click=\"nextMonth\">&gt;</button>\n    </div>\n    \n    <div class=\"calendar-grid\">\n      <div class=\"day-header\" v-for=\"day in dayHeaders\" :key=\"day\">{{ day }}</div>\n      \n      <div \n        v-for=\"date in calendarDates\" \n        :key=\"date.key\"\n        :class=\"['calendar-date', {\n          'other-month': !date.isCurrentMonth,\n          'has-event': date.hasEvent,\n          'today': date.isToday\n        }]\"\n        @click=\"date.hasEvent && selectDate(date)\"\n      >\n        {{ date.day }}\n        <div v-if=\"date.hasEvent\" class=\"event-indicator\"></div>\n      </div>\n    </div>\n\n    <!-- Event details modal -->\n    <div v-if=\"selectedDateEvents.length > 0\" class=\"modal-overlay\" @click=\"closeEventDetails\">\n      <div class=\"modal\" @click.stop>\n        <h3>Events on {{ formatSelectedDate }}</h3>\n        <div v-for=\"event in selectedDateEvents\" :key=\"event.id\" class=\"event-summary\">\n          <h4>{{ event.name }}</h4>\n          <p>{{ event.description }}</p>\n          <p><strong>Fee:</strong> ${{ event.fee }}</p>\n          <p><strong>Remaining slots:</strong> {{ event.remainingSlots }}</p>\n          <button\n            v-if=\"event.remainingSlots > 0\"\n            @click=\"openRegistrationForm(event)\"\n            class=\"register-btn\"\n          >\n            Register for Event\n          </button>\n          <p v-else class=\"full-event\">Event is full</p>\n        </div>\n        <button :st @click=\"closeEventDetails\">Close</button>\n      </div>\n    </div>\n\n    <!-- Registration Form Component -->\n    <EventRegistrationForm\n      :show=\"showRegistrationForm\"\n      :event=\"selectedEvent || {}\"\n      @close=\"closeRegistrationForm\"\n      @registration-success=\"handleRegistrationSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport EventRegistrationForm from './EventRegistrationForm.vue'\n\nexport default {\n  name: 'EventCalendar',\n  components: {\n    EventRegistrationForm\n  },\n  props: {\n    events: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      currentDate: new Date(),\n      selectedDate: null,\n      selectedDateEvents: [],\n      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n      showRegistrationForm: false,\n      selectedEvent: null\n    }\n  },\n  computed: {\n    monthYear() {\n      return this.currentDate.toLocaleDateString('en-US', {\n        month: 'long',\n        year: 'numeric'\n      })\n    },\n    \n    formatSelectedDate() {\n      return this.selectedDate ? this.selectedDate.toLocaleDateString() : ''\n    },\n    \n    calendarDates() {\n      const year = this.currentDate.getFullYear()\n      const month = this.currentDate.getMonth()\n\n      const firstDay = new Date(year, month, 1)\n      const startDate = new Date(firstDay)\n      startDate.setDate(startDate.getDate() - firstDay.getDay())\n      \n      const dates = []\n      const today = new Date()\n      \n      for (let i = 0; i < 42; i++) {\n        const date = new Date(startDate)\n        date.setDate(startDate.getDate() + i)\n        \n        const hasEvent = this.hasEventOnDate(date)\n        \n        dates.push({\n          key: date.toISOString(),\n          day: date.getDate(),\n          date: new Date(date),\n          isCurrentMonth: date.getMonth() === month,\n          hasEvent,\n          isToday: this.isSameDay(date, today)\n        })\n      }\n      \n      return dates\n    }\n  },\n  methods: {\n    previousMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1)\n    },\n    \n    nextMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1)\n    },\n    \n    hasEventOnDate(date) {\n      return this.events.some(event => \n        this.isSameDay(new Date(event.date), date)\n      )\n    },\n    \n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() &&\n             date1.getMonth() === date2.getMonth() &&\n             date1.getFullYear() === date2.getFullYear()\n    },\n    \n    selectDate(dateObj) {\n      this.selectedDate = dateObj.date\n      this.selectedDateEvents = this.events.filter(event =>\n        this.isSameDay(new Date(event.date), dateObj.date)\n      )\n    },\n    \n    closeEventDetails() {\n      this.selectedDate = null\n      this.selectedDateEvents = []\n    },\n\n    openRegistrationForm(event) {\n      this.selectedEvent = event\n      this.showRegistrationForm = true\n    },\n\n    closeRegistrationForm() {\n      this.showRegistrationForm = false\n      this.selectedEvent = null\n    },\n\n    handleRegistrationSuccess(event) {\n      // Update the event's remaining slots\n      if (event && event.remainingSlots > 0) {\n        event.remainingSlots -= 1\n      }\n\n      // Emit event to parent to refresh events if needed\n      this.$emit('registration-success')\n\n      // Close the registration form\n      this.closeRegistrationForm()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.calendar-container {\n  max-width: 800px;\n  margin: 20px auto;\n  padding: 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.calendar-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.calendar-header button {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 10px 15px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.calendar-grid {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 1px;\n  background: #ddd;\n}\n\n.day-header {\n  background: #34495e;\n  color: white;\n  padding: 10px;\n  text-align: center;\n  font-weight: bold;\n}\n\n.calendar-date {\n  background: white;\n  padding: 10px;\n  min-height: 60px;\n  position: relative;\n  cursor: pointer;\n  display: flex;\n  align-items: flex-start;\n}\n\n.calendar-date.other-month {\n  color: #bdc3c7;\n  background: #f8f9fa;\n}\n\n.calendar-date.today {\n  background: #e8f4fd;\n  font-weight: bold;\n}\n\n.calendar-date.has-event {\n  background: #d5f4e6;\n  cursor: pointer;\n}\n\n.calendar-date.has-event:hover {\n  background: #a8e6cf;\n}\n\n.event-indicator {\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  width: 8px;\n  height: 8px;\n  background: #e74c3c;\n  border-radius: 50%;\n}\n\n.event-summary {\n  border-bottom: 1px solid #eee;\n  padding: 15px 0;\n}\n\n.event-summary:last-child {\n  border-bottom: none;\n}\n\n.register-btn {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: bold;\n  width: 100%;\n  transition: background 0.2s;\n}\n\n.register-btn:hover {\n  background: #219a52;\n}\n\n.full-event {\n  color: #e74c3c;\n  font-weight: bold;\n  margin-top: 10px;\n}\n\n\n\n.event-fee-modal {\n  color: #e67e22;\n  font-weight: bold;\n  font-size: 16px;\n  margin: 10px 0 20px 0;\n}\n\n.form-group small {\n  color: #666;\n  font-size: 12px;\n  margin-top: 5px;\n  display: block;\n}\n\n.success-message {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: #27ae60;\n  color: white;\n  padding: 15px 20px;\n  border-radius: 5px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.2);\n  z-index: 1001;\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": ";;;;AA4DA,OAAAA,qBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,WAAA,MAAAC,IAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACA,YAAAR,WAAA,CAAAS,kBAAA;QACAC,KAAA;QACAC,IAAA;MACA;IACA;IAEAC,mBAAA;MACA,YAAAV,YAAA,QAAAA,YAAA,CAAAO,kBAAA;IACA;IAEAI,cAAA;MACA,MAAAF,IAAA,QAAAX,WAAA,CAAAc,WAAA;MACA,MAAAJ,KAAA,QAAAV,WAAA,CAAAe,QAAA;MAEA,MAAAC,QAAA,OAAAf,IAAA,CAAAU,IAAA,EAAAD,KAAA;MACA,MAAAO,SAAA,OAAAhB,IAAA,CAAAe,QAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA,KAAAH,QAAA,CAAAI,MAAA;MAEA,MAAAC,KAAA;MACA,MAAAC,KAAA,OAAArB,IAAA;MAEA,SAAAsB,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,MAAAC,IAAA,OAAAvB,IAAA,CAAAgB,SAAA;QACAO,IAAA,CAAAN,OAAA,CAAAD,SAAA,CAAAE,OAAA,KAAAI,CAAA;QAEA,MAAAE,QAAA,QAAAC,cAAA,CAAAF,IAAA;QAEAH,KAAA,CAAAM,IAAA;UACAC,GAAA,EAAAJ,IAAA,CAAAK,WAAA;UACAC,GAAA,EAAAN,IAAA,CAAAL,OAAA;UACAK,IAAA,MAAAvB,IAAA,CAAAuB,IAAA;UACAO,cAAA,EAAAP,IAAA,CAAAT,QAAA,OAAAL,KAAA;UACAe,QAAA;UACAO,OAAA,OAAAC,SAAA,CAAAT,IAAA,EAAAF,KAAA;QACA;MACA;MAEA,OAAAD,KAAA;IACA;EACA;EACAa,OAAA;IACAC,cAAA;MACA,KAAAnC,WAAA,OAAAC,IAAA,MAAAD,WAAA,CAAAc,WAAA,SAAAd,WAAA,CAAAe,QAAA;IACA;IAEAqB,UAAA;MACA,KAAApC,WAAA,OAAAC,IAAA,MAAAD,WAAA,CAAAc,WAAA,SAAAd,WAAA,CAAAe,QAAA;IACA;IAEAW,eAAAF,IAAA;MACA,YAAA7B,MAAA,CAAA0C,IAAA,CAAAC,KAAA,IACA,KAAAL,SAAA,KAAAhC,IAAA,CAAAqC,KAAA,CAAAd,IAAA,GAAAA,IAAA,CACA;IACA;IAEAS,UAAAM,KAAA,EAAAC,KAAA;MACA,OAAAD,KAAA,CAAApB,OAAA,OAAAqB,KAAA,CAAArB,OAAA,MACAoB,KAAA,CAAAxB,QAAA,OAAAyB,KAAA,CAAAzB,QAAA,MACAwB,KAAA,CAAAzB,WAAA,OAAA0B,KAAA,CAAA1B,WAAA;IACA;IAEA2B,WAAAC,OAAA;MACA,KAAAxC,YAAA,GAAAwC,OAAA,CAAAlB,IAAA;MACA,KAAArB,kBAAA,QAAAR,MAAA,CAAAgD,MAAA,CAAAL,KAAA,IACA,KAAAL,SAAA,KAAAhC,IAAA,CAAAqC,KAAA,CAAAd,IAAA,GAAAkB,OAAA,CAAAlB,IAAA,CACA;IACA;IAEAoB,kBAAA;MACA,KAAA1C,YAAA;MACA,KAAAC,kBAAA;IACA;IAEA0C,qBAAAP,KAAA;MACA,KAAAhC,aAAA,GAAAgC,KAAA;MACA,KAAAjC,oBAAA;IACA;IAEAyC,sBAAA;MACA,KAAAzC,oBAAA;MACA,KAAAC,aAAA;IACA;IAEAyC,0BAAAT,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAU,cAAA;QACAV,KAAA,CAAAU,cAAA;MACA;;MAEA;MACA,KAAAC,KAAA;;MAEA;MACA,KAAAH,qBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}