<template>
  <div>
    <!-- Modal -->
    <div v-if="show" class="modal-overlay" @click="closeForm">
      <div class="modal registration-modal" @click.stop>
        <div class="modal-header">
          <h3>Register for {{ event.name }}</h3>
          <button class="close-btn" @click="closeForm">&times;</button>
        </div>

        <div class="event-info">
          <p class="event-fee">Registration Fee: <span class="fee-amount">${{ event.fee }}</span></p>
          <p class="remaining-slots">
            {{ event.remainingSlots }} slots remaining
          </p>
        </div>

        <form @submit.prevent="submitRegistration" class="registration-form">
          <div class="form-row">
            <div class="form-group">
              <label for="userFullName">
                <i class="icon-user"></i>
                Full Name *
              </label>
              <input
                type="text"
                id="userFullName"
                v-model="form.userFullName"
                placeholder="Enter your full name"
                required
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="email">
                <i class="icon-mail"></i>
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                v-model="form.email"
                placeholder="<EMAIL>"
                required
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="contactNumber">
                <i class="icon-phone"></i>
                Contact Number *
              </label>
              <input
                type="tel"
                id="contactNumber"
                v-model="form.contactNumber"
                placeholder="60123456789"
                required
                class="form-input"
              />
              <small class="form-hint">Must start with 60</small>
            </div>
          </div>

          <div class="form-row two-columns">
            <div class="form-group">
              <label for="nric">
                <i class="icon-id"></i>
                NRIC *
              </label>
              <input
                type="text"
                id="nric"
                v-model="form.nric"
                placeholder="123456-78-9012"
                required
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label for="age">
                <i class="icon-calendar"></i>
                Age *
              </label>
              <input
                type="number"
                id="age"
                v-model="form.age"
                placeholder="25"
                min="1"
                max="120"
                required
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row two-columns">
            <div class="form-group">
              <label for="country">
                <i class="icon-globe"></i>
                Country *
              </label>
              <input
                type="text"
                id="country"
                v-model="form.country"
                placeholder="Malaysia"
                required
                class="form-input"
              />
            </div>

            <div class="form-group">
              <label for="gender">
                <i class="icon-user-check"></i>
                Gender *
              </label>
              <select
                id="gender"
                v-model="form.gender"
                required
                class="form-input form-select"
              >
                <option value="">Select Gender</option>
                <option value="M">Male</option>
                <option value="F">Female</option>
              </select>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" @click="closeForm" class="btn btn-secondary">
              Cancel
            </button>
            <button type="submit" :disabled="isSubmitting" class="btn btn-primary">
              <span v-if="isSubmitting" class="loading-spinner"></span>
              {{ isSubmitting ? 'Registering...' : 'Register Now' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="success-toast">
      <i class="icon-check"></i>
      {{ successMessage }}
    </div>
  </div>
</template>

<script>
import { buildApiUrl, API_ENDPOINTS } from '../config/api.js'

export default {
  name: 'EventRegistrationForm',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    event: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isSubmitting: false,
      successMessage: '',
      form: {
        userFullName: '',
        email: '',
        contactNumber: '',
        nric: '',
        age: '',
        country: '',
        gender: ''
      }
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm()
      }
    }
  },
  methods: {
    closeForm() {
      this.$emit('close')
    },
    
    resetForm() {
      this.form = {
        userFullName: '',
        email: '',
        contactNumber: '',
        nric: '',
        age: '',
        country: '',
        gender: ''
      }
      this.successMessage = ''
    },
    
    validatePhone(phone) {
      return phone.startsWith('60')
    },
    
    async submitRegistration() {
      if (!this.validatePhone(this.form.contactNumber)) {
        alert('Contact number must start with 60')
        return
      }
      
      this.isSubmitting = true
      try {
        const registrationUrl = buildApiUrl(API_ENDPOINTS.EVENT_REGISTER(this.event.id))
        console.log('Attempting registration to:', registrationUrl)
        
        const response = await fetch(registrationUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.form)
        })
        
        console.log('Response status:', response.status)
        console.log('Response URL:', response.url)
        
        if (!response.ok) {
          if (response.url.includes('localhost') && !registrationUrl.includes('localhost')) {
            throw new Error('Registration endpoint not available. The server may be redirecting to localhost, which suggests the endpoint does not exist.')
          }
          
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
          throw new Error(errorData.message || `Registration failed with status ${response.status}`)
        }
        
        await response.json()
        this.successMessage = `Successfully registered for ${this.event.name}!`
        
        // Emit success event to parent
        this.$emit('registration-success', this.event)
        
        // Close form after short delay
        setTimeout(() => {
          this.closeForm()
          this.successMessage = ''
        }, 2000)
        
      } catch (err) {
        console.error('Registration error:', err)
        
        if (err.message.includes('Failed to fetch')) {
          alert('Registration failed: Unable to connect to the server. The registration endpoint may not be available yet.')
        } else if (err.message.includes('CORS')) {
          alert('Registration failed: Server configuration issue (CORS). Please contact support.')
        } else {
          alert(`Registration failed: ${err.message}`)
        }
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style scoped>
/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.59);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* Modal Container */
.registration-modal {
  background: rgb(245, 251, 249);
  border-radius: 12px;
  max-width: 600px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Event Info */
.event-info {
  padding: 0 24px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #c8e4ff;
  margin: 0 24px 20px;
  border-radius: 8px;
  padding: 16px;
}

.event-fee {
  margin: 0;
  color: #374151;
  font-weight: 500;
}

.fee-amount {
  color: #059669;
  font-weight: 700;
  font-size: 18px;
}

.remaining-slots {
  margin: 0;
  color: #6f8fcf;
  font-size: 14px;
}

/* Form Styles */
.registration-form {
  padding: 0 24px 24px;
}

.form-row {
  margin-bottom: 20px;
}

.form-row.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.form-group label i {
  margin-right: 8px;
  width: 16px;
  color: #6b7280;
}

.form-input {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  cursor: pointer;
}

.form-hint {
  margin-top: 4px;
  color: #6b7280;
  font-size: 12px;
}

/* Buttons */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Loading Spinner */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Success Toast */
.success-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #059669;
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Icons (using simple CSS shapes as placeholders) */
.icon-user::before { content: "👤"; }
.icon-mail::before { content: "📧"; }
.icon-phone::before { content: "📱"; }
.icon-id::before { content: "🆔"; }
.icon-calendar::before { content: "📅"; }
.icon-globe::before { content: "🌍"; }
.icon-user-check::before { content: "👥"; }
.icon-users::before { content: "👥"; }
.icon-check::before { content: "✅"; }

/* Responsive Design */
@media (max-width: 640px) {
  .registration-modal {
    width: 100%;
    height: 100%;
    border-radius: 0;
    max-height: 100vh;
  }
  
  .form-row.two-columns {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .event-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
