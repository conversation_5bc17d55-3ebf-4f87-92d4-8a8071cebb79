{"ast": null, "code": "export default {\n  name: 'EventBooking',\n  data() {\n    return {\n      events: [],\n      loading: true,\n      error: null,\n      showModal: false,\n      selectedEvent: null,\n      submitting: false,\n      successMessage: '',\n      registrationForm: {\n        name: '',\n        phone: '',\n        email: ''\n      }\n    };\n  },\n  mounted() {\n    this.fetchEvents();\n  },\n  methods: {\n    async fetchEvents() {\n      try {\n        const response = await fetch('http://quiz.vilor.com/api/events/listing');\n        if (!response.ok) throw new Error('Failed to fetch events');\n        this.events = await response.json();\n      } catch (err) {\n        this.error = 'Failed to load events. Please try again later.';\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    openRegistration(event) {\n      this.selectedEvent = event;\n      this.showModal = true;\n      this.resetForm();\n    },\n    closeModal() {\n      this.showModal = false;\n      this.selectedEvent = null;\n      this.resetForm();\n    },\n    resetForm() {\n      this.registrationForm = {\n        name: '',\n        phone: '',\n        email: ''\n      };\n    },\n    validatePhone(phone) {\n      return phone.startsWith('+60') || phone.startsWith('60');\n    },\n    async submitRegistration() {\n      if (!this.validatePhone(this.registrationForm.phone)) {\n        alert('Phone number must start with +60 or 60');\n        return;\n      }\n      this.submitting = true;\n      try {\n        const response = await fetch('http://quiz.vilor.com/api/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            eventId: this.selectedEvent.id,\n            ...this.registrationForm\n          })\n        });\n        if (!response.ok) throw new Error('Registration failed');\n        this.successMessage = `Successfully registered for ${this.selectedEvent.title}!`;\n        this.closeModal();\n        this.fetchEvents(); // Refresh events to update remaining slots\n\n        setTimeout(() => {\n          this.successMessage = '';\n        }, 5000);\n      } catch (err) {\n        alert('Registration failed. Please try again.');\n      } finally {\n        this.submitting = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "events", "loading", "error", "showModal", "selectedEvent", "submitting", "successMessage", "registrationForm", "phone", "email", "mounted", "fetchEvents", "methods", "response", "fetch", "ok", "Error", "json", "err", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "openRegistration", "event", "resetForm", "closeModal", "validatePhone", "startsWith", "submitRegistration", "alert", "method", "headers", "body", "JSON", "stringify", "eventId", "id", "title", "setTimeout"], "sources": ["src/components/EventBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"event-booking\">\r\n    <h1>Event Booking System</h1>\r\n\r\n     <!-- Calendar Component -->\r\n    <EventCalendar :events=\"events\" />\r\n    \r\n    \r\n    <!-- Loading state -->\r\n    <div v-if=\"loading\" class=\"loading\">Loading events...</div>\r\n    \r\n    <!-- Error state -->\r\n    <div v-if=\"error\" class=\"error\">{{ error }}</div>\r\n    \r\n    <!-- Events list -->\r\n    <div v-if=\"!loading && !error\" class=\"events-container\">\r\n    <!-- Event Cards -->\r\n    <h2>All Event</h2>\r\n      <div v-for=\"event in events\" :key=\"event.id\" class=\"event-card\">\r\n        <img :src=\"event.coverImage\" :alt=\"event.name\" class=\"event-image\" />\r\n        <div class=\"event-content\">\r\n          <h3>{{ event.name }}</h3>\r\n          <p class=\"event-description\">{{ event.description }}</p>\r\n          <p class=\"event-date\">{{ formatDate(event.date) }}</p>\r\n          <p class=\"event-slots\">Remaining slots: {{ event.remainingSlots }}</p>\r\n          \r\n          <button \r\n            v-if=\"event.remainingSlots > 0\" \r\n            @click=\"openRegistration(event)\"\r\n            class=\"register-btn\"\r\n          >\r\n            Register Now\r\n          </button>\r\n          <p v-else class=\"no-slots\">Sorry, this event is fully booked!</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Registration Modal -->\r\n    <div v-if=\"showModal\" class=\"modal-overlay\" @click=\"closeModal\">\r\n      <div class=\"modal\" @click.stop>\r\n        <h3>Register for {{ selectedEvent.title }}</h3>\r\n        <form @submit.prevent=\"submitRegistration\">\r\n          <div class=\"form-group\">\r\n            <label>Name:</label>\r\n            <input v-model=\"registrationForm.name\" type=\"text\" required />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label>Phone Number:</label>\r\n            <input v-model=\"registrationForm.phone\" type=\"tel\" required />\r\n            <small>Must start with +60 or 60</small>\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label>Email:</label>\r\n            <input v-model=\"registrationForm.email\" type=\"email\" required />\r\n          </div>\r\n          <div class=\"form-actions\">\r\n            <button type=\"submit\" :disabled=\"submitting\">\r\n              {{ submitting ? 'Registering...' : 'Register' }}\r\n            </button>\r\n            <button type=\"button\" @click=\"closeModal\">Cancel</button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div v-if=\"successMessage\" class=\"success-message\">\r\n      {{ successMessage }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'EventBooking',\r\n  data() {\r\n    return {\r\n      events: [],\r\n      loading: true,\r\n      error: null,\r\n      showModal: false,\r\n      selectedEvent: null,\r\n      submitting: false,\r\n      successMessage: '',\r\n      registrationForm: {\r\n        name: '',\r\n        phone: '',\r\n        email: ''\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchEvents()\r\n  },\r\n  methods: {\r\n    async fetchEvents() {\r\n      try {\r\n        const response = await fetch('http://quiz.vilor.com/api/events/listing')\r\n        if (!response.ok) throw new Error('Failed to fetch events')\r\n        this.events = await response.json()\r\n      } catch (err) {\r\n        this.error = 'Failed to load events. Please try again later.'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n      })\r\n    },\r\n    \r\n    openRegistration(event) {\r\n      this.selectedEvent = event\r\n      this.showModal = true\r\n      this.resetForm()\r\n    },\r\n    \r\n    closeModal() {\r\n      this.showModal = false\r\n      this.selectedEvent = null\r\n      this.resetForm()\r\n    },\r\n    \r\n    resetForm() {\r\n      this.registrationForm = {\r\n        name: '',\r\n        phone: '',\r\n        email: ''\r\n      }\r\n    },\r\n    \r\n    validatePhone(phone) {\r\n      return phone.startsWith('+60') || phone.startsWith('60')\r\n    },\r\n    \r\n    async submitRegistration() {\r\n      if (!this.validatePhone(this.registrationForm.phone)) {\r\n        alert('Phone number must start with +60 or 60')\r\n        return\r\n      }\r\n      \r\n      this.submitting = true\r\n      try {\r\n        const response = await fetch('http://quiz.vilor.com/api/register', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          },\r\n          body: JSON.stringify({\r\n            eventId: this.selectedEvent.id,\r\n            ...this.registrationForm\r\n          })\r\n        })\r\n        \r\n        if (!response.ok) throw new Error('Registration failed')\r\n        \r\n        this.successMessage = `Successfully registered for ${this.selectedEvent.title}!`\r\n        this.closeModal()\r\n        this.fetchEvents() // Refresh events to update remaining slots\r\n        \r\n        setTimeout(() => {\r\n          this.successMessage = ''\r\n        }, 5000)\r\n        \r\n      } catch (err) {\r\n        alert('Registration failed. Please try again.')\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.event-booking {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.events-container {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.event-card {\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.event-card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n}\r\n\r\n.event-content {\r\n  padding: 15px;\r\n}\r\n\r\n.event-description {\r\n  color: #666;\r\n  margin: 10px 0;\r\n}\r\n\r\n.event-date {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.event-slots {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n}\r\n\r\n.register-btn {\r\n  background: #3498db;\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: #2980b9;\r\n}\r\n\r\n.no-slots {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n}\r\n\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0,0,0,0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal {\r\n  background: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 500px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-group small {\r\n  color: #666;\r\n  font-size: 12px;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n}\r\n\r\n.form-actions button {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\n.form-actions button[type=\"submit\"] {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.form-actions button[type=\"button\"] {\r\n  background: #95a5a6;\r\n  color: white;\r\n}\r\n\r\n.success-message {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: #27ae60;\r\n  color: white;\r\n  padding: 15px 20px;\r\n  border-radius: 5px;\r\n  z-index: 1001;\r\n}\r\n</style>"], "mappings": "AA0EA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,cAAA;MACAC,gBAAA;QACAT,IAAA;QACAU,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAD,YAAA;MACA;QACA,MAAAE,QAAA,SAAAC,KAAA;QACA,KAAAD,QAAA,CAAAE,EAAA,YAAAC,KAAA;QACA,KAAAhB,MAAA,SAAAa,QAAA,CAAAI,IAAA;MACA,SAAAC,GAAA;QACA,KAAAhB,KAAA;MACA;QACA,KAAAD,OAAA;MACA;IACA;IAEAkB,WAAAC,UAAA;MACA,WAAAC,IAAA,CAAAD,UAAA,EAAAE,kBAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEAC,iBAAAC,KAAA;MACA,KAAAvB,aAAA,GAAAuB,KAAA;MACA,KAAAxB,SAAA;MACA,KAAAyB,SAAA;IACA;IAEAC,WAAA;MACA,KAAA1B,SAAA;MACA,KAAAC,aAAA;MACA,KAAAwB,SAAA;IACA;IAEAA,UAAA;MACA,KAAArB,gBAAA;QACAT,IAAA;QACAU,KAAA;QACAC,KAAA;MACA;IACA;IAEAqB,cAAAtB,KAAA;MACA,OAAAA,KAAA,CAAAuB,UAAA,WAAAvB,KAAA,CAAAuB,UAAA;IACA;IAEA,MAAAC,mBAAA;MACA,UAAAF,aAAA,MAAAvB,gBAAA,CAAAC,KAAA;QACAyB,KAAA;QACA;MACA;MAEA,KAAA5B,UAAA;MACA;QACA,MAAAQ,QAAA,SAAAC,KAAA;UACAoB,MAAA;UACAC,OAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA;YACAC,OAAA,OAAAnC,aAAA,CAAAoC,EAAA;YACA,QAAAjC;UACA;QACA;QAEA,KAAAM,QAAA,CAAAE,EAAA,YAAAC,KAAA;QAEA,KAAAV,cAAA,uCAAAF,aAAA,CAAAqC,KAAA;QACA,KAAAZ,UAAA;QACA,KAAAlB,WAAA;;QAEA+B,UAAA;UACA,KAAApC,cAAA;QACA;MAEA,SAAAY,GAAA;QACAe,KAAA;MACA;QACA,KAAA5B,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}