{"ast": null, "code": "export const API_BASE_URL = \"https://quiz.vilor.com\";\nexport const buildApiUrl = endpoint => {\n  return `${API_BASE_URL}${endpoint}`;\n};", "map": {"version": 3, "names": ["API_BASE_URL", "buildApiUrl", "endpoint"], "sources": ["D:/sem6/myself/vue/my-vue-project1/src/config/api.js"], "sourcesContent": ["export const API_BASE_URL = \"https://quiz.vilor.com\"\r\n\r\nexport const buildApiUrl = (endpoint) => {\r\n  return `${API_BASE_URL}${endpoint}`\r\n}"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAG,wBAAwB;AAEpD,OAAO,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EACvC,OAAO,GAAGF,YAAY,GAAGE,QAAQ,EAAE;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}