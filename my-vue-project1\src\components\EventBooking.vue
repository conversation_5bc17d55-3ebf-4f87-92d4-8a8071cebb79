<template>
  <div class="event-booking">
    <h1>Event Booking System</h1>
    
    <!-- Calendar Component -->
    <EventCalendar :events="events" />
    
    <!-- Loading state -->
    <div v-if="loading" class="loading">Loading events...</div>
    
    <!-- Error state -->
    <div v-if="error" class="error">{{ error }}</div>
    
    <!-- Events list -->
    <div v-if="!loading && !error" class="events-container">
      <h2>All Events</h2>
      <div class="events-grid">
        <div v-for="event in events" :key="event.id" class="event-card">
          <img :src="event.img" :alt="event.name" class="event-image" />
          <div class="event-content">
            <h3>{{ event.name }}</h3>
            <p class="event-description">{{ event.description }}</p>
            <p class="event-date">{{ formatDate(event.date) }}</p>
            <p class="event-fee">Fee: ${{ event.fee }}</p>
            <p class="event-capacity">Capacity: {{ event.capacity }}</p>
            <p class="event-slots">Remaining slots: {{ event.remainingSlots }}</p>
            
            <button 
              v-if="event.remainingSlots > 0" 
              @click="openRegistration(event)"
              class="register-btn"
            >
              Register Now
            </button>
            <p v-else class="no-slots">Sorry, this event is fully booked!</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Registration Form Component -->
    <EventRegistrationForm
      :show="showModal"
      :event="selectedEvent || {}"
      @close="closeModal"
      @registration-success="handleRegistrationSuccess"
    />
  </div>
</template>

<script>
import EventCalendar from './EventCalendar.vue'
import EventRegistrationForm from './EventRegistrationForm.vue'
import { buildApiUrl, API_ENDPOINTS } from '../config/api.js'

export default {
  name: 'EventBooking',
  components: {
    EventCalendar,
    EventRegistrationForm
  },
  data() {
    return {
      events: [],
      loading: true,
      error: null,
      showModal: false,
      selectedEvent: null
    }
  },
  mounted() {
    this.fetchEvents()
  },
  methods: {
    async fetchEvents() {
      try {
        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING))
        if (!response.ok) throw new Error('Failed to fetch events')
        this.events = await response.json()
      } catch (err) {
        this.error = 'Failed to load events. Please try again later.'
        console.error('Error fetching events:', err)
      } finally {
        this.loading = false
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    
    openRegistration(event) {
      this.selectedEvent = event
      this.showModal = true
      this.resetForm()
    },
    
    closeModal() {
      this.showModal = false
      this.selectedEvent = null
    },

    handleRegistrationSuccess(event) {
      // Update the event's remaining slots
      if (event && event.remainingSlots > 0) {
        event.remainingSlots -= 1
      }

      // Refresh events to get latest data
      this.fetchEvents()

      // Close the modal
      this.closeModal()
    }
  }
}
</script>

<style scoped>
.event-booking {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 20px;
  font-size: 18px;
}

.error {
  color: #e74c3c;
}

.events-container {
  margin-top: 30px;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.event-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
  background: white;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.event-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.event-content {
  padding: 20px;
}

.event-content h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.event-description {
  color: #666;
  margin: 10px 0;
  line-height: 1.5;
}

.event-date {
  font-weight: bold;
  color: #2c3e50;
  margin: 8px 0;
}

.event-fee {
  color: #e67e22;
  font-weight: bold;
  font-size: 18px;
  margin: 8px 0;
}

.event-capacity {
  color: #7f8c8d;
  margin: 5px 0;
}

.event-slots {
  color: #27ae60;
  font-weight: bold;
  margin: 10px 0;
}

.register-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  transition: background 0.2s;
}

.register-btn:hover {
  background: #2980b9;
}

.no-slots {
  color: #e74c3c;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  background: #fdf2f2;
  border-radius: 5px;
  margin: 0;
}


</style>
