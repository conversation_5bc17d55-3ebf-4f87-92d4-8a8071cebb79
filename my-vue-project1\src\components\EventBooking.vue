<template>
  <div class="event-booking">
    <h1>Event Booking System</h1>
    
    <!-- Calendar Component -->
    <EventCalendar :events="events" />
    
    <!-- Loading state -->
    <div v-if="loading" class="loading">Loading events...</div>
    
    <!-- Error state -->
    <div v-if="error" class="error">{{ error }}</div>
    
    <!-- Events list -->
    <div v-if="!loading && !error" class="events-container">
      <h2>All Events</h2>
      <div class="events-grid">
        <div v-for="event in events" :key="event.id" class="event-card">
          <img :src="event.img" :alt="event.name" class="event-image" />
          <div class="event-content">
            <h3>{{ event.name }}</h3>
            <p class="event-description">{{ event.description }}</p>
            <p class="event-date">{{ formatDate(event.date) }}</p>
            <p class="event-fee">Fee: ${{ event.fee }}</p>
            <p class="event-capacity">Capacity: {{ event.capacity }}</p>
            <p class="event-slots">Remaining slots: {{ event.remainingSlots }}</p>
            
            <button 
              v-if="event.remainingSlots > 0" 
              @click="openRegistration(event)"
              class="register-btn"
            >
              Register Now
            </button>
            <p v-else class="no-slots">Sorry, this event is fully booked!</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Registration Modal -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <h3>Register for {{ selectedEvent.name }}</h3>
        <p class="event-fee-modal">Registration Fee: ${{ selectedEvent.fee }}</p>
        <form @submit.prevent="submitRegistration">
          <div class="form-group">
            <label for="userFullName">Full Name *</label>
            <input
              type="text"
              id="userFullName"
              v-model="registrationForm.userFullName"
              required
            />
          </div>

          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              v-model="registrationForm.email"
              required
            />
          </div>

          <div class="form-group">
            <label for="contactNumber">Contact Number *</label>
            <input
              type="tel"
              id="contactNumber"
              v-model="registrationForm.contactNumber"
              placeholder="60xxxxxxxxx"
              required
            />
            <small>Must start with 60</small>
          </div>

          <div class="form-group">
            <label for="nric">NRIC *</label>
            <input
              type="text"
              id="nric"
              v-model="registrationForm.nric"
              required
            />
          </div>

          <div class="form-group">
            <label for="age">Age *</label>
            <input
              type="number"
              id="age"
              v-model="registrationForm.age"
              min="1"
              required
            />
          </div>

          <div class="form-group">
            <label for="country">Country *</label>
            <input
              type="text"
              id="country"
              v-model="registrationForm.country"
              required
            />
          </div>

          <div class="form-group">
            <label for="gender">Gender *</label>
            <select
              id="gender"
              v-model="registrationForm.gender"
              required
            >
              <option value="">Select Gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="submit" :disabled="submitting">
              {{ submitting ? 'Registering...' : 'Register' }}
            </button>
            <button type="button" @click="closeModal">Cancel</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="success-message">
      {{ successMessage }}
    </div>
  </div>
</template>

<script>
import EventCalendar from './EventCalendar.vue'
import { buildApiUrl } from '../config/api.js'

export default {
  name: 'EventBooking',
  components: {
    EventCalendar
  },
  data() {
    return {
      events: [],
      loading: true,
      error: null,
      showModal: false,
      selectedEvent: null,
      submitting: false,
      successMessage: '',
      registrationForm: {
        userFullName: '',
        email: '',
        contactNumber: '',
        nric: '',
        age: '',
        country: '',
        gender: ''
      }
    }
  },
  mounted() {
    this.fetchEvents()
  },
  methods: {
    async fetchEvents() {
      try {
        const response = await fetch(buildApiUrl('/api/events/listing'))
        if (!response.ok) throw new Error('Failed to fetch events')
        this.events = await response.json()
      } catch (err) {
        this.error = 'Failed to load events. Please try again later.'
        console.error('Error fetching events:', err)
      } finally {
        this.loading = false
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    
    openRegistration(event) {
      this.selectedEvent = event
      this.showModal = true
      this.resetForm()
    },
    
    closeModal() {
      this.showModal = false
      this.selectedEvent = null
      this.resetForm()
    },
    
    resetForm() {
      this.registrationForm = {
        userFullName: '',
        email: '',
        contactNumber: '',
        nric: '',
        age: '',
        country: '',
        gender: ''
      }
    },
    
    validatePhone(phone) {
      return phone.startsWith('60')
    },

    async submitRegistration() {
      if (!this.validatePhone(this.registrationForm.contactNumber)) {
        alert('Contact number must start with 60')
        return
      }

      this.submitting = true
      try {
        const response = await fetch(buildApiUrl(`/api/events/${this.selectedEvent.id}/register`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.registrationForm)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || 'Registration failed')
        }

        await response.json()
        this.successMessage = `Successfully registered for ${this.selectedEvent.name}!`
        this.closeModal()
        this.fetchEvents() // Refresh events to update remaining slots

        setTimeout(() => {
          this.successMessage = ''
        }, 5000)

      } catch (err) {
        alert(`Registration failed: ${err.message}`)
        console.error('Registration error:', err)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.event-booking {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 20px;
  font-size: 18px;
}

.error {
  color: #e74c3c;
}

.events-container {
  margin-top: 30px;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.event-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
  background: white;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.event-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.event-content {
  padding: 20px;
}

.event-content h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.event-description {
  color: #666;
  margin: 10px 0;
  line-height: 1.5;
}

.event-date {
  font-weight: bold;
  color: #2c3e50;
  margin: 8px 0;
}

.event-fee {
  color: #e67e22;
  font-weight: bold;
  font-size: 18px;
  margin: 8px 0;
}

.event-capacity {
  color: #7f8c8d;
  margin: 5px 0;
}

.event-slots {
  color: #27ae60;
  font-weight: bold;
  margin: 10px 0;
}

.register-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  width: 100%;
  transition: background 0.2s;
}

.register-btn:hover {
  background: #2980b9;
}

.no-slots {
  color: #e74c3c;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  background: #fdf2f2;
  border-radius: 5px;
  margin: 0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.event-fee-modal {
  color: #e67e22;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 20px;
  text-align: center;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 25px;
}

.form-actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background 0.2s;
}

.form-actions button[type="submit"] {
  background: #27ae60;
  color: white;
}

.form-actions button[type="submit"]:hover {
  background: #229954;
}

.form-actions button[type="submit"]:disabled {
  background: #95a5a6;
  cursor: not-allowed;
}

.form-actions button[type="button"] {
  background: #95a5a6;
  color: white;
}

.form-actions button[type="button"]:hover {
  background: #7f8c8d;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #27ae60;
  color: white;
  padding: 15px 20px;
  border-radius: 5px;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
