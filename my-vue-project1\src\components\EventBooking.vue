<template>
  <div class="event-booking">
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="gradient-text">Event Booking</span>
          <span class="hero-subtitle">System</span>
        </h1>
        <p class="hero-description">
          Discover amazing events and book your spot with ease. Browse through our calendar or explore all available events below.
        </p>
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">{{ events.length }}</span>
            <span class="stat-label">Events Available</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ totalSlots }}</span>
            <span class="stat-label">Total Slots</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ availableSlots }}</span>
            <span class="stat-label">Available Slots</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Loading state -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading amazing events...</p>
      </div>

      <!-- Error state -->
      <div v-if="error" class="error-container">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
          <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
        </svg>
        <h3>Oops! Something went wrong</h3>
        <p>{{ error }}</p>
        <button @click="fetchEvents" class="retry-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" stroke="currentColor" stroke-width="2"/>
          </svg>
          Try Again
        </button>
      </div>

      <!-- Content Layout with Sidebar -->
      <div v-if="!loading && !error" class="content-layout">
        <!-- Main Events Section -->
        <div class="events-main">
          <div class="section-header">
            <h2 class="section-title">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z" stroke="currentColor" stroke-width="2"/>
                <path d="M21 2L16 7L13 4" stroke="currentColor" stroke-width="2"/>
              </svg>
              All Events
            </h2>
            <p class="section-description">Browse all available events and secure your spot</p>
          </div>

          <!-- Filter and Sort Controls -->
          <div class="controls-bar">
            <div class="search-box">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
              </svg>
              <input
                type="text"
                placeholder="Search events..."
                v-model="searchQuery"
                class="search-input"
              />
            </div>
            <div class="filter-controls">
              <select v-model="sortBy" class="sort-select">
                <option value="date">Sort by Date</option>
                <option value="name">Sort by Name</option>
                <option value="fee">Sort by Fee</option>
                <option value="slots">Sort by Available Slots</option>
              </select>
            </div>
          </div>

          <div class="events-grid">
            <div v-for="event in filteredAndSortedEvents" :key="event.id" class="event-card">
              <div class="event-image-container">
                <img :src="event.img" :alt="event.name" class="event-image" />
                <div class="event-badge" :class="{ 'badge-full': event.remainingSlots === 0, 'badge-low': event.remainingSlots <= 5 && event.remainingSlots > 0 }">
                  {{ event.remainingSlots === 0 ? 'FULL' : event.remainingSlots <= 5 ? 'FILLING FAST' : 'AVAILABLE' }}
                </div>
              </div>
              <div class="event-content">
                <div class="event-header">
                  <h3 class="event-title">{{ event.name }}</h3>
                  <div class="event-price">${{ event.fee }}</div>
                </div>
                <p class="event-description">{{ event.description }}</p>

                <div class="event-details">
                  <div class="detail-item">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                      <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                      <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                      <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>{{ formatDate(event.date) }}</span>
                  </div>
                  <div class="detail-item">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7ZM23 21V19C23 17.9391 22.5786 16.9217 21.8284 16.1716C21.0783 15.4214 20.0609 15 19 15H17M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55332C18.7122 5.25592 19.0078 6.11872 19.0078 7.005C19.0078 7.89128 18.7122 8.75408 18.1676 9.45668C17.623 10.1593 16.8604 10.6597 16 10.88" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>{{ event.capacity }} capacity</span>
                  </div>
                  <div class="detail-item">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span class="slots-indicator" :class="{ 'slots-low': event.remainingSlots <= 5, 'slots-full': event.remainingSlots === 0 }">
                      {{ event.remainingSlots }} slots left
                    </span>
                  </div>
                </div>

                <div class="event-actions">
                  <button
                    v-if="event.remainingSlots > 0"
                    @click="openRegistration(event)"
                    class="register-btn primary"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M12.5 7.5C12.5 9.98528 10.4853 12 8 12C5.51472 12 3.5 9.98528 3.5 7.5C3.5 5.01472 5.51472 3 8 3C10.4853 3 12.5 5.01472 12.5 7.5ZM20 8V14M23 11H17" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Register Now
                  </button>
                  <div v-else class="no-slots">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                      <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                      <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Event Full
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="filteredAndSortedEvents.length === 0" class="empty-state">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
            </svg>
            <h3>No events found</h3>
            <p>Try adjusting your search criteria or check back later for new events.</p>
          </div>
        </div>

        <!-- Calendar Sidebar -->
        <div class="calendar-sidebar">
          <div class="sidebar-header">
            <h3 class="sidebar-title">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
              </svg>
              Event Calendar
            </h3>
            <p class="sidebar-description">Click on dates to view events</p>
          </div>
          <div class="calendar-wrapper">
            <EventCalendar :events="events" @registration-success="handleRegistrationSuccess" />
          </div>
        </div>
      </div>
    </div>

    <!-- Registration Form Component -->
    <EventRegistrationForm
      :show="showModal"
      :event="selectedEvent || {}"
      @close="closeModal"
      @registration-success="handleRegistrationSuccess"
    />
  </div>
</template>

<script>
import EventCalendar from './EventCalendar.vue'
import EventRegistrationForm from './EventRegistrationForm.vue'
import { buildApiUrl, API_ENDPOINTS } from '../config/api.js'

export default {
  name: 'EventBooking',
  components: {
    EventCalendar,
    EventRegistrationForm
  },
  data() {
    return {
      events: [],
      loading: true,
      error: null,
      showModal: false,
      selectedEvent: null,
      searchQuery: '',
      sortBy: 'date'
    }
  },
  computed: {
    totalSlots() {
      return this.events.reduce((total, event) => total + event.capacity, 0)
    },
    availableSlots() {
      return this.events.reduce((total, event) => total + event.remainingSlots, 0)
    },
    filteredAndSortedEvents() {
      let filtered = this.events

      // Filter by search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(event =>
          event.name.toLowerCase().includes(query) ||
          event.description.toLowerCase().includes(query)
        )
      }

      // Sort events
      return filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name)
          case 'fee':
            return a.fee - b.fee
          case 'slots':
            return b.remainingSlots - a.remainingSlots
          case 'date':
          default:
            return new Date(a.date) - new Date(b.date)
        }
      })
    }
  },
  mounted() {
    this.fetchEvents()
  },
  methods: {
    async fetchEvents() {
      try {
        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING))
        if (!response.ok) throw new Error('Failed to fetch events')
        this.events = await response.json()
      } catch (err) {
        this.error = 'Failed to load events. Please try again later.'
        console.error('Error fetching events:', err)
      } finally {
        this.loading = false
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    
    openRegistration(event) {
      this.selectedEvent = event
      this.showModal = true
      this.resetForm()
    },
    
    closeModal() {
      this.showModal = false
      this.selectedEvent = null
    },

    handleRegistrationSuccess(event) {
      // Update the event's remaining slots
      if (event && event.remainingSlots > 0) {
        event.remainingSlots -= 1
      }

      // Refresh events to get latest data
      this.fetchEvents()

      // Close the modal
      this.closeModal()
    }
  }
}
</script>

<style scoped>
/* Global Styles */
.event-booking {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin: 0 0 20px 0;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  display: block;
  font-size: 2.5rem;
  font-weight: 300;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0 0 40px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  opacity: 0.8;
  margin-top: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Main Content Layout */
.main-content {
  background: white;
  margin: 0;
  padding: 80px 20px;
}

.content-layout {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  align-items: start;
}

.events-main {
  min-width: 0; /* Prevents grid overflow */
}

/* Calendar Sidebar */
.calendar-sidebar {
  background: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.sidebar-header {
  margin-bottom: 24px;
  text-align: center;
}

.sidebar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.sidebar-title svg {
  color: #3b82f6;
}

.sidebar-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.calendar-wrapper .calendar-container {
  margin: 0;
  padding: 0;
  background: transparent;
  box-shadow: none;
}

.section-header {
  max-width: 1200px;
  margin: 0 auto 60px auto;
  text-align: center;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.section-title svg {
  color: #3b82f6;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Loading and Error States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  text-align: center;
}

.error-container svg {
  color: #ef4444;
  margin-bottom: 20px;
}

.error-container h3 {
  font-size: 1.5rem;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.error-container p {
  color: #6b7280;
  margin: 0 0 24px 0;
}

.retry-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.retry-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Controls Bar */
.controls-bar {
  margin: 0 0 40px 0;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box svg {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.sort-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-select:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.event-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.event-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.event-image-container {
  position: relative;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.event-card:hover .event-image {
  transform: scale(1.05);
}

.event-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #10b981;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-badge.badge-low {
  background: #f59e0b;
}

.event-badge.badge-full {
  background: #ef4444;
}

.event-content {
  padding: 24px;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.event-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  flex: 1;
  line-height: 1.3;
}

.event-price {
  background: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1.125rem;
  margin-left: 16px;
}

.event-description {
  color: #6b7280;
  margin: 0 0 20px 0;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.detail-item svg {
  color: #9ca3af;
  flex-shrink: 0;
}

.slots-indicator {
  font-weight: 600;
}

.slots-indicator.slots-low {
  color: #f59e0b;
}

.slots-indicator.slots-full {
  color: #ef4444;
}

.event-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.register-btn.primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: center;
}

.register-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.no-slots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #ef4444;
  font-weight: 600;
  padding: 14px 28px;
  background: #fef2f2;
  border: 2px solid #fecaca;
  border-radius: 12px;
  width: 100%;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  max-width: 500px;
  margin: 0 auto;
}

.empty-state svg {
  color: #9ca3af;
  margin-bottom: 24px;
}

.empty-state h3 {
  font-size: 1.5rem;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.empty-state p {
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr 350px;
    gap: 30px;
  }

  .calendar-sidebar {
    padding: 20px;
  }
}

@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .calendar-sidebar {
    position: static;
    max-height: none;
    order: -1; /* Show calendar above events on mobile */
  }

  .events-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.75rem;
  }

  .hero-stats {
    gap: 30px;
    flex-wrap: wrap;
  }

  .stat-number {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 8px;
  }

  .controls-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .events-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .event-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .event-price {
    margin-left: 0;
    align-self: flex-start;
  }

  .main-content {
    padding: 60px 16px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 16px;
  }

  .calendar-section,
  .events-section {
    padding: 60px 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .hero-stats {
    gap: 20px;
  }

  .stat-number {
    font-size: 1.75rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .event-content {
    padding: 20px;
  }
}
</style>
