{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nexport default {\n  name: 'EventCalendar',\n  props: {\n    events: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      currentDate: new Date(),\n      selectedDate: null,\n      selectedDateEvents: [],\n      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n      showRegistrationForm: false,\n      selectedEvent: null,\n      isSubmitting: false,\n      registrationForm: {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    };\n  },\n  computed: {\n    monthYear() {\n      return this.currentDate.toLocaleDateString('en-US', {\n        month: 'long',\n        year: 'numeric'\n      });\n    },\n    formatSelectedDate() {\n      return this.selectedDate ? this.selectedDate.toLocaleDateString() : '';\n    },\n    calendarDates() {\n      const year = this.currentDate.getFullYear();\n      const month = this.currentDate.getMonth();\n      const firstDay = new Date(year, month, 1);\n      const startDate = new Date(firstDay);\n      startDate.setDate(startDate.getDate() - firstDay.getDay());\n      const dates = [];\n      const today = new Date();\n      for (let i = 0; i < 42; i++) {\n        const date = new Date(startDate);\n        date.setDate(startDate.getDate() + i);\n        const hasEvent = this.hasEventOnDate(date);\n        dates.push({\n          key: date.toISOString(),\n          day: date.getDate(),\n          date: new Date(date),\n          isCurrentMonth: date.getMonth() === month,\n          hasEvent,\n          isToday: this.isSameDay(date, today)\n        });\n      }\n      return dates;\n    }\n  },\n  methods: {\n    previousMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);\n    },\n    nextMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);\n    },\n    hasEventOnDate(date) {\n      return this.events.some(event => this.isSameDay(new Date(event.date), date));\n    },\n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();\n    },\n    selectDate(dateObj) {\n      this.selectedDate = dateObj.date;\n      this.selectedDateEvents = this.events.filter(event => this.isSameDay(new Date(event.date), dateObj.date));\n    },\n    closeEventDetails() {\n      this.selectedDate = null;\n      this.selectedDateEvents = [];\n    },\n    openRegistrationForm(event) {\n      this.selectedEvent = event;\n      this.showRegistrationForm = true;\n      this.resetRegistrationForm();\n    },\n    closeRegistrationForm() {\n      this.showRegistrationForm = false;\n      this.selectedEvent = null;\n      this.resetRegistrationForm();\n    },\n    resetRegistrationForm() {\n      this.registrationForm = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      };\n    },\n    async submitRegistration() {\n      if (this.isSubmitting) return;\n      this.isSubmitting = true;\n      try {\n        const response = await fetch(`/api/events/${this.selectedEvent.id}/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.registrationForm)\n        });\n        if (response.ok) {\n          await response.json();\n          alert('Registration successful!');\n\n          // Update the event's remaining slots\n          this.selectedEvent.remainingSlots -= 1;\n\n          // Close the registration form\n          this.closeRegistrationForm();\n        } else {\n          const errorData = await response.json();\n          alert(`Registration failed: ${errorData.message || 'Unknown error'}`);\n        }\n      } catch (error) {\n        console.error('Registration error:', error);\n        alert('Registration failed. Please try again.');\n      } finally {\n        this.isSubmitting = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "events", "type", "Array", "default", "data", "currentDate", "Date", "selectedDate", "selectedDateEvents", "dayHeaders", "showRegistrationForm", "selectedEvent", "isSubmitting", "registrationForm", "userFullName", "email", "contactNumber", "nric", "age", "country", "gender", "computed", "monthYear", "toLocaleDateString", "month", "year", "formatSelectedDate", "calendarDates", "getFullYear", "getMonth", "firstDay", "startDate", "setDate", "getDate", "getDay", "dates", "today", "i", "date", "hasEvent", "hasEventOnDate", "push", "key", "toISOString", "day", "isCurrentMonth", "isToday", "isSameDay", "methods", "previousMonth", "nextMonth", "some", "event", "date1", "date2", "selectDate", "date<PERSON><PERSON>j", "filter", "closeEventDetails", "openRegistrationForm", "resetRegistrationForm", "closeRegistrationForm", "submitRegistration", "response", "fetch", "id", "method", "headers", "body", "JSON", "stringify", "ok", "json", "alert", "remainingSlots", "errorData", "message", "error", "console"], "sources": ["src/components/EventCalendar.vue"], "sourcesContent": ["<template>\n  <div class=\"calendar-container\">\n    <h2>Event Calendar</h2>\n    <div class=\"calendar-header\">\n      <button @click=\"previousMonth\">&lt;</button>\n      <h3>{{ monthYear }}</h3>\n      <button @click=\"nextMonth\">&gt;</button>\n    </div>\n    \n    <div class=\"calendar-grid\">\n      <div class=\"day-header\" v-for=\"day in dayHeaders\" :key=\"day\">{{ day }}</div>\n      \n      <div \n        v-for=\"date in calendarDates\" \n        :key=\"date.key\"\n        :class=\"['calendar-date', {\n          'other-month': !date.isCurrentMonth,\n          'has-event': date.hasEvent,\n          'today': date.isToday\n        }]\"\n        @click=\"date.hasEvent && selectDate(date)\"\n      >\n        {{ date.day }}\n        <div v-if=\"date.hasEvent\" class=\"event-indicator\"></div>\n      </div>\n    </div>\n\n    <!-- Event details modal -->\n    <div v-if=\"selectedDateEvents.length > 0\" class=\"modal-overlay\" @click=\"closeEventDetails\">\n      <div class=\"modal\" @click.stop>\n        <h3>Events on {{ formatSelectedDate }}</h3>\n        <div v-for=\"event in selectedDateEvents\" :key=\"event.id\" class=\"event-summary\">\n          <h4>{{ event.name }}</h4>\n          <p>{{ event.description }}</p>\n          <p><strong>Fee:</strong> ${{ event.fee }}</p>\n          <p><strong>Remaining slots:</strong> {{ event.remainingSlots }}</p>\n          <button\n            v-if=\"event.remainingSlots > 0\"\n            @click=\"openRegistrationForm(event)\"\n            class=\"register-btn\"\n          >\n            Register for Event\n          </button>\n          <p v-else class=\"full-event\">Event is full</p>\n        </div>\n        <button @click=\"closeEventDetails\">Close</button>\n      </div>\n    </div>\n\n    <!-- Registration form modal -->\n    <div v-if=\"showRegistrationForm\" class=\"modal-overlay\" @click=\"closeRegistrationForm\">\n      <div class=\"modal registration-modal\" @click.stop>\n        <h3>Register for {{ selectedEvent.name }}</h3>\n        <form @submit.prevent=\"submitRegistration\">\n          <div class=\"form-group\">\n            <label for=\"userFullName\">Full Name *</label>\n            <input\n              type=\"text\"\n              id=\"userFullName\"\n              v-model=\"registrationForm.userFullName\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"email\">Email *</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              v-model=\"registrationForm.email\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"contactNumber\">Contact Number *</label>\n            <input\n              type=\"tel\"\n              id=\"contactNumber\"\n              v-model=\"registrationForm.contactNumber\"\n              placeholder=\"60xxxxxxxxx\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"nric\">NRIC *</label>\n            <input\n              type=\"text\"\n              id=\"nric\"\n              v-model=\"registrationForm.nric\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"age\">Age *</label>\n            <input\n              type=\"number\"\n              id=\"age\"\n              v-model=\"registrationForm.age\"\n              min=\"1\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"country\">Country *</label>\n            <input\n              type=\"text\"\n              id=\"country\"\n              v-model=\"registrationForm.country\"\n              required\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"gender\">Gender *</label>\n            <select\n              id=\"gender\"\n              v-model=\"registrationForm.gender\"\n              required\n            >\n              <option value=\"\">Select Gender</option>\n              <option value=\"male\">Male</option>\n              <option value=\"female\">Female</option>\n              <option value=\"other\">Other</option>\n            </select>\n          </div>\n\n          <div class=\"form-actions\">\n            <button type=\"button\" @click=\"closeRegistrationForm\">Cancel</button>\n            <button type=\"submit\" :disabled=\"isSubmitting\">\n              {{ isSubmitting ? 'Registering...' : 'Register' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'EventCalendar',\n  props: {\n    events: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      currentDate: new Date(),\n      selectedDate: null,\n      selectedDateEvents: [],\n      dayHeaders: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\n      showRegistrationForm: false,\n      selectedEvent: null,\n      isSubmitting: false,\n      registrationForm: {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    }\n  },\n  computed: {\n    monthYear() {\n      return this.currentDate.toLocaleDateString('en-US', {\n        month: 'long',\n        year: 'numeric'\n      })\n    },\n    \n    formatSelectedDate() {\n      return this.selectedDate ? this.selectedDate.toLocaleDateString() : ''\n    },\n    \n    calendarDates() {\n      const year = this.currentDate.getFullYear()\n      const month = this.currentDate.getMonth()\n\n      const firstDay = new Date(year, month, 1)\n      const startDate = new Date(firstDay)\n      startDate.setDate(startDate.getDate() - firstDay.getDay())\n      \n      const dates = []\n      const today = new Date()\n      \n      for (let i = 0; i < 42; i++) {\n        const date = new Date(startDate)\n        date.setDate(startDate.getDate() + i)\n        \n        const hasEvent = this.hasEventOnDate(date)\n        \n        dates.push({\n          key: date.toISOString(),\n          day: date.getDate(),\n          date: new Date(date),\n          isCurrentMonth: date.getMonth() === month,\n          hasEvent,\n          isToday: this.isSameDay(date, today)\n        })\n      }\n      \n      return dates\n    }\n  },\n  methods: {\n    previousMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1)\n    },\n    \n    nextMonth() {\n      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1)\n    },\n    \n    hasEventOnDate(date) {\n      return this.events.some(event => \n        this.isSameDay(new Date(event.date), date)\n      )\n    },\n    \n    isSameDay(date1, date2) {\n      return date1.getDate() === date2.getDate() &&\n             date1.getMonth() === date2.getMonth() &&\n             date1.getFullYear() === date2.getFullYear()\n    },\n    \n    selectDate(dateObj) {\n      this.selectedDate = dateObj.date\n      this.selectedDateEvents = this.events.filter(event =>\n        this.isSameDay(new Date(event.date), dateObj.date)\n      )\n    },\n    \n    closeEventDetails() {\n      this.selectedDate = null\n      this.selectedDateEvents = []\n    },\n\n    openRegistrationForm(event) {\n      this.selectedEvent = event\n      this.showRegistrationForm = true\n      this.resetRegistrationForm()\n    },\n\n    closeRegistrationForm() {\n      this.showRegistrationForm = false\n      this.selectedEvent = null\n      this.resetRegistrationForm()\n    },\n\n    resetRegistrationForm() {\n      this.registrationForm = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    },\n\n    async submitRegistration() {\n      if (this.isSubmitting) return\n\n      this.isSubmitting = true\n\n      try {\n        const response = await fetch(`/api/events/${this.selectedEvent.id}/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(this.registrationForm)\n        })\n\n        if (response.ok) {\n          await response.json()\n          alert('Registration successful!')\n\n          // Update the event's remaining slots\n          this.selectedEvent.remainingSlots -= 1\n\n          // Close the registration form\n          this.closeRegistrationForm()\n        } else {\n          const errorData = await response.json()\n          alert(`Registration failed: ${errorData.message || 'Unknown error'}`)\n        }\n      } catch (error) {\n        console.error('Registration error:', error)\n        alert('Registration failed. Please try again.')\n      } finally {\n        this.isSubmitting = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.calendar-container {\n  max-width: 800px;\n  margin: 20px auto;\n  padding: 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.calendar-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.calendar-header button {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 10px 15px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.calendar-grid {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 1px;\n  background: #ddd;\n}\n\n.day-header {\n  background: #34495e;\n  color: white;\n  padding: 10px;\n  text-align: center;\n  font-weight: bold;\n}\n\n.calendar-date {\n  background: white;\n  padding: 10px;\n  min-height: 60px;\n  position: relative;\n  cursor: pointer;\n  display: flex;\n  align-items: flex-start;\n}\n\n.calendar-date.other-month {\n  color: #bdc3c7;\n  background: #f8f9fa;\n}\n\n.calendar-date.today {\n  background: #e8f4fd;\n  font-weight: bold;\n}\n\n.calendar-date.has-event {\n  background: #d5f4e6;\n  cursor: pointer;\n}\n\n.calendar-date.has-event:hover {\n  background: #a8e6cf;\n}\n\n.event-indicator {\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  width: 8px;\n  height: 8px;\n  background: #e74c3c;\n  border-radius: 50%;\n}\n\n.event-summary {\n  border-bottom: 1px solid #eee;\n  padding: 15px 0;\n}\n\n.event-summary:last-child {\n  border-bottom: none;\n}\n</style>\n"], "mappings": ";;;;AA+IA;EACAA,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,WAAA,MAAAC,IAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACA,YAAAjB,WAAA,CAAAkB,kBAAA;QACAC,KAAA;QACAC,IAAA;MACA;IACA;IAEAC,mBAAA;MACA,YAAAnB,YAAA,QAAAA,YAAA,CAAAgB,kBAAA;IACA;IAEAI,cAAA;MACA,MAAAF,IAAA,QAAApB,WAAA,CAAAuB,WAAA;MACA,MAAAJ,KAAA,QAAAnB,WAAA,CAAAwB,QAAA;MAEA,MAAAC,QAAA,OAAAxB,IAAA,CAAAmB,IAAA,EAAAD,KAAA;MACA,MAAAO,SAAA,OAAAzB,IAAA,CAAAwB,QAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA,KAAAH,QAAA,CAAAI,MAAA;MAEA,MAAAC,KAAA;MACA,MAAAC,KAAA,OAAA9B,IAAA;MAEA,SAAA+B,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,MAAAC,IAAA,OAAAhC,IAAA,CAAAyB,SAAA;QACAO,IAAA,CAAAN,OAAA,CAAAD,SAAA,CAAAE,OAAA,KAAAI,CAAA;QAEA,MAAAE,QAAA,QAAAC,cAAA,CAAAF,IAAA;QAEAH,KAAA,CAAAM,IAAA;UACAC,GAAA,EAAAJ,IAAA,CAAAK,WAAA;UACAC,GAAA,EAAAN,IAAA,CAAAL,OAAA;UACAK,IAAA,MAAAhC,IAAA,CAAAgC,IAAA;UACAO,cAAA,EAAAP,IAAA,CAAAT,QAAA,OAAAL,KAAA;UACAe,QAAA;UACAO,OAAA,OAAAC,SAAA,CAAAT,IAAA,EAAAF,KAAA;QACA;MACA;MAEA,OAAAD,KAAA;IACA;EACA;EACAa,OAAA;IACAC,cAAA;MACA,KAAA5C,WAAA,OAAAC,IAAA,MAAAD,WAAA,CAAAuB,WAAA,SAAAvB,WAAA,CAAAwB,QAAA;IACA;IAEAqB,UAAA;MACA,KAAA7C,WAAA,OAAAC,IAAA,MAAAD,WAAA,CAAAuB,WAAA,SAAAvB,WAAA,CAAAwB,QAAA;IACA;IAEAW,eAAAF,IAAA;MACA,YAAAtC,MAAA,CAAAmD,IAAA,CAAAC,KAAA,IACA,KAAAL,SAAA,KAAAzC,IAAA,CAAA8C,KAAA,CAAAd,IAAA,GAAAA,IAAA,CACA;IACA;IAEAS,UAAAM,KAAA,EAAAC,KAAA;MACA,OAAAD,KAAA,CAAApB,OAAA,OAAAqB,KAAA,CAAArB,OAAA,MACAoB,KAAA,CAAAxB,QAAA,OAAAyB,KAAA,CAAAzB,QAAA,MACAwB,KAAA,CAAAzB,WAAA,OAAA0B,KAAA,CAAA1B,WAAA;IACA;IAEA2B,WAAAC,OAAA;MACA,KAAAjD,YAAA,GAAAiD,OAAA,CAAAlB,IAAA;MACA,KAAA9B,kBAAA,QAAAR,MAAA,CAAAyD,MAAA,CAAAL,KAAA,IACA,KAAAL,SAAA,KAAAzC,IAAA,CAAA8C,KAAA,CAAAd,IAAA,GAAAkB,OAAA,CAAAlB,IAAA,CACA;IACA;IAEAoB,kBAAA;MACA,KAAAnD,YAAA;MACA,KAAAC,kBAAA;IACA;IAEAmD,qBAAAP,KAAA;MACA,KAAAzC,aAAA,GAAAyC,KAAA;MACA,KAAA1C,oBAAA;MACA,KAAAkD,qBAAA;IACA;IAEAC,sBAAA;MACA,KAAAnD,oBAAA;MACA,KAAAC,aAAA;MACA,KAAAiD,qBAAA;IACA;IAEAA,sBAAA;MACA,KAAA/C,gBAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;IAEA,MAAA0C,mBAAA;MACA,SAAAlD,YAAA;MAEA,KAAAA,YAAA;MAEA;QACA,MAAAmD,QAAA,SAAAC,KAAA,qBAAArD,aAAA,CAAAsD,EAAA;UACAC,MAAA;UACAC,OAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,MAAAzD,gBAAA;QACA;QAEA,IAAAkD,QAAA,CAAAQ,EAAA;UACA,MAAAR,QAAA,CAAAS,IAAA;UACAC,KAAA;;UAEA;UACA,KAAA9D,aAAA,CAAA+D,cAAA;;UAEA;UACA,KAAAb,qBAAA;QACA;UACA,MAAAc,SAAA,SAAAZ,QAAA,CAAAS,IAAA;UACAC,KAAA,yBAAAE,SAAA,CAAAC,OAAA;QACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,wBAAAA,KAAA;QACAJ,KAAA;MACA;QACA,KAAA7D,YAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}