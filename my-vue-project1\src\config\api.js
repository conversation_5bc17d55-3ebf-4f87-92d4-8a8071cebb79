// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://quiz.vilor.com',
  ENDPOINTS: {
    EVENTS: {
      LISTING: '/api/events/listing',
      REGISTER: (eventId) => `/api/events/${eventId}/register`
    }
  }
}

// Helper function to build full API URLs
export const buildApiUrl = (endpoint) => {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

// API service functions
export const apiService = {
  // Get events listing
  getEvents: () => {
    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.LISTING))
  },
  
  // Register for an event
  registerForEvent: (eventId, registrationData) => {
    return fetch(buildApiUrl(API_CONFIG.ENDPOINTS.EVENTS.REGISTER(eventId)), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registrationData)
    })
  }
}
