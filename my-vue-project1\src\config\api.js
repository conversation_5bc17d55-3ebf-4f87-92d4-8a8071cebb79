// API Configuration
export const API_BASE_URL = "https://quiz.vilor.com"

// Build API URL helper
export const buildApiUrl = (endpoint) => {
  return `${API_BASE_URL}${endpoint}`
}

// API Endpoints
export const API_ENDPOINTS = {
  EVENTS_LISTING: '/api/events/listing',
  // Note: The registration endpoint might not exist yet on the server
  // You may need to check with your backend team about the correct endpoint
  EVENT_REGISTER: (eventId) => `/api/events/${eventId}/register`
}