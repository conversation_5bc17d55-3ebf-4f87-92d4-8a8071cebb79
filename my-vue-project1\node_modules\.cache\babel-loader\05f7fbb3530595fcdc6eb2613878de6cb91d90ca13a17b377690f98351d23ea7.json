{"ast": null, "code": "import EventCalendar from './EventCalendar.vue';\nimport EventRegistrationForm from './EventRegistrationForm.vue';\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js';\nexport default {\n  name: 'EventBooking',\n  components: {\n    EventCalendar,\n    EventRegistrationForm\n  },\n  data() {\n    return {\n      events: [],\n      loading: true,\n      error: null,\n      showModal: false,\n      selectedEvent: null\n    };\n  },\n  mounted() {\n    this.fetchEvents();\n  },\n  methods: {\n    async fetchEvents() {\n      try {\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING));\n        if (!response.ok) throw new Error('Failed to fetch events');\n        this.events = await response.json();\n      } catch (err) {\n        this.error = 'Failed to load events. Please try again later.';\n        console.error('Error fetching events:', err);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    openRegistration(event) {\n      this.selectedEvent = event;\n      this.showModal = true;\n      this.resetForm();\n    },\n    closeModal() {\n      this.showModal = false;\n      this.selectedEvent = null;\n      this.resetForm();\n    },\n    resetForm() {\n      this.registrationForm = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      };\n    },\n    validatePhone(phone) {\n      return phone.startsWith('60');\n    },\n    async submitRegistration() {\n      if (!this.validatePhone(this.registrationForm.contactNumber)) {\n        alert('Contact number must start with 60');\n        return;\n      }\n      this.submitting = true;\n      try {\n        const registrationUrl = buildApiUrl(API_ENDPOINTS.EVENT_REGISTER(this.selectedEvent.id));\n        console.log('Attempting registration to:', registrationUrl);\n        const response = await fetch(registrationUrl, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.registrationForm)\n        });\n        console.log('Response status:', response.status);\n        console.log('Response URL:', response.url);\n        if (!response.ok) {\n          // Check if it's a CORS/redirect issue\n          if (response.url.includes('localhost') && !registrationUrl.includes('localhost')) {\n            throw new Error('Registration endpoint not available. The server may be redirecting to localhost, which suggests the endpoint does not exist.');\n          }\n          const errorData = await response.json().catch(() => ({\n            message: 'Unknown error'\n          }));\n          throw new Error(errorData.message || `Registration failed with status ${response.status}`);\n        }\n        await response.json();\n        this.successMessage = `Successfully registered for ${this.selectedEvent.name}!`;\n        this.closeModal();\n        this.fetchEvents(); // Refresh events to update remaining slots\n\n        setTimeout(() => {\n          this.successMessage = '';\n        }, 5000);\n      } catch (err) {\n        console.error('Registration error:', err);\n\n        // Provide more helpful error messages\n        if (err.message.includes('Failed to fetch')) {\n          alert('Registration failed: Unable to connect to the server. The registration endpoint may not be available yet.');\n        } else if (err.message.includes('CORS')) {\n          alert('Registration failed: Server configuration issue (CORS). Please contact support.');\n        } else {\n          alert(`Registration failed: ${err.message}`);\n        }\n      } finally {\n        this.submitting = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["EventCalendar", "EventRegistrationForm", "buildApiUrl", "API_ENDPOINTS", "name", "components", "data", "events", "loading", "error", "showModal", "selectedEvent", "mounted", "fetchEvents", "methods", "response", "fetch", "EVENTS_LISTING", "ok", "Error", "json", "err", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "openRegistration", "event", "resetForm", "closeModal", "registrationForm", "userFullName", "email", "contactNumber", "nric", "age", "country", "gender", "validatePhone", "phone", "startsWith", "submitRegistration", "alert", "submitting", "registrationUrl", "EVENT_REGISTER", "id", "log", "method", "headers", "body", "JSON", "stringify", "status", "url", "includes", "errorData", "catch", "message", "successMessage", "setTimeout"], "sources": ["src/components/EventBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"event-booking\">\r\n    <h1>Event Booking System</h1>\r\n    \r\n    <!-- Calendar Component -->\r\n    <EventCalendar :events=\"events\" />\r\n    \r\n    <!-- Loading state -->\r\n    <div v-if=\"loading\" class=\"loading\">Loading events...</div>\r\n    \r\n    <!-- Error state -->\r\n    <div v-if=\"error\" class=\"error\">{{ error }}</div>\r\n    \r\n    <!-- Events list -->\r\n    <div v-if=\"!loading && !error\" class=\"events-container\">\r\n      <h2>All Events</h2>\r\n      <div class=\"events-grid\">\r\n        <div v-for=\"event in events\" :key=\"event.id\" class=\"event-card\">\r\n          <img :src=\"event.img\" :alt=\"event.name\" class=\"event-image\" />\r\n          <div class=\"event-content\">\r\n            <h3>{{ event.name }}</h3>\r\n            <p class=\"event-description\">{{ event.description }}</p>\r\n            <p class=\"event-date\">{{ formatDate(event.date) }}</p>\r\n            <p class=\"event-fee\">Fee: ${{ event.fee }}</p>\r\n            <p class=\"event-capacity\">Capacity: {{ event.capacity }}</p>\r\n            <p class=\"event-slots\">Remaining slots: {{ event.remainingSlots }}</p>\r\n            \r\n            <button \r\n              v-if=\"event.remainingSlots > 0\" \r\n              @click=\"openRegistration(event)\"\r\n              class=\"register-btn\"\r\n            >\r\n              Register Now\r\n            </button>\r\n            <p v-else class=\"no-slots\">Sorry, this event is fully booked!</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Registration Form Component -->\r\n    <EventRegistrationForm\r\n      :show=\"showModal\"\r\n      :event=\"selectedEvent || {}\"\r\n      @close=\"closeModal\"\r\n      @registration-success=\"handleRegistrationSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport EventCalendar from './EventCalendar.vue'\r\nimport EventRegistrationForm from './EventRegistrationForm.vue'\r\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js'\r\n\r\nexport default {\r\n  name: 'EventBooking',\r\n  components: {\r\n    EventCalendar,\r\n    EventRegistrationForm\r\n  },\r\n  data() {\r\n    return {\r\n      events: [],\r\n      loading: true,\r\n      error: null,\r\n      showModal: false,\r\n      selectedEvent: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchEvents()\r\n  },\r\n  methods: {\r\n    async fetchEvents() {\r\n      try {\r\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING))\r\n        if (!response.ok) throw new Error('Failed to fetch events')\r\n        this.events = await response.json()\r\n      } catch (err) {\r\n        this.error = 'Failed to load events. Please try again later.'\r\n        console.error('Error fetching events:', err)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n      })\r\n    },\r\n    \r\n    openRegistration(event) {\r\n      this.selectedEvent = event\r\n      this.showModal = true\r\n      this.resetForm()\r\n    },\r\n    \r\n    closeModal() {\r\n      this.showModal = false\r\n      this.selectedEvent = null\r\n      this.resetForm()\r\n    },\r\n    \r\n    resetForm() {\r\n      this.registrationForm = {\r\n        userFullName: '',\r\n        email: '',\r\n        contactNumber: '',\r\n        nric: '',\r\n        age: '',\r\n        country: '',\r\n        gender: ''\r\n      }\r\n    },\r\n    \r\n    validatePhone(phone) {\r\n      return phone.startsWith('60')\r\n    },\r\n\r\n    async submitRegistration() {\r\n      if (!this.validatePhone(this.registrationForm.contactNumber)) {\r\n        alert('Contact number must start with 60')\r\n        return\r\n      }\r\n\r\n      this.submitting = true\r\n      try {\r\n        const registrationUrl = buildApiUrl(API_ENDPOINTS.EVENT_REGISTER(this.selectedEvent.id))\r\n        console.log('Attempting registration to:', registrationUrl)\r\n\r\n        const response = await fetch(registrationUrl, {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          },\r\n          body: JSON.stringify(this.registrationForm)\r\n        })\r\n\r\n        console.log('Response status:', response.status)\r\n        console.log('Response URL:', response.url)\r\n\r\n        if (!response.ok) {\r\n          // Check if it's a CORS/redirect issue\r\n          if (response.url.includes('localhost') && !registrationUrl.includes('localhost')) {\r\n            throw new Error('Registration endpoint not available. The server may be redirecting to localhost, which suggests the endpoint does not exist.')\r\n          }\r\n\r\n          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))\r\n          throw new Error(errorData.message || `Registration failed with status ${response.status}`)\r\n        }\r\n\r\n        await response.json()\r\n        this.successMessage = `Successfully registered for ${this.selectedEvent.name}!`\r\n        this.closeModal()\r\n        this.fetchEvents() // Refresh events to update remaining slots\r\n\r\n        setTimeout(() => {\r\n          this.successMessage = ''\r\n        }, 5000)\r\n\r\n      } catch (err) {\r\n        console.error('Registration error:', err)\r\n\r\n        // Provide more helpful error messages\r\n        if (err.message.includes('Failed to fetch')) {\r\n          alert('Registration failed: Unable to connect to the server. The registration endpoint may not be available yet.')\r\n        } else if (err.message.includes('CORS')) {\r\n          alert('Registration failed: Server configuration issue (CORS). Please contact support.')\r\n        } else {\r\n          alert(`Registration failed: ${err.message}`)\r\n        }\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.event-booking {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.events-container {\r\n  margin-top: 30px;\r\n}\r\n\r\n.events-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.event-card {\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  transition: transform 0.2s;\r\n  background: white;\r\n}\r\n\r\n.event-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n}\r\n\r\n.event-content {\r\n  padding: 20px;\r\n}\r\n\r\n.event-content h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.event-description {\r\n  color: #666;\r\n  margin: 10px 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.event-date {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-fee {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-capacity {\r\n  color: #7f8c8d;\r\n  margin: 5px 0;\r\n}\r\n\r\n.event-slots {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n  margin: 10px 0;\r\n}\r\n\r\n.register-btn {\r\n  background: #3498db;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  width: 100%;\r\n  transition: background 0.2s;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: #2980b9;\r\n}\r\n\r\n.no-slots {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  padding: 12px;\r\n  background: #fdf2f2;\r\n  border-radius: 5px;\r\n  margin: 0;\r\n}\r\n\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0,0,0,0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal {\r\n  background: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 500px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.event-fee-modal {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.form-group input {\r\n  width: 100%;\r\n  padding: 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.form-group input:focus {\r\n  outline: none;\r\n  border-color: #3498db;\r\n}\r\n\r\n.form-group small {\r\n  color: #666;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n  display: block;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n  margin-top: 25px;\r\n}\r\n\r\n.form-actions button {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: background 0.2s;\r\n}\r\n\r\n.form-actions button[type=\"submit\"] {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.form-actions button[type=\"submit\"]:hover {\r\n  background: #229954;\r\n}\r\n\r\n.form-actions button[type=\"submit\"]:disabled {\r\n  background: #95a5a6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.form-actions button[type=\"button\"] {\r\n  background: #95a5a6;\r\n  color: white;\r\n}\r\n\r\n.form-actions button[type=\"button\"]:hover {\r\n  background: #7f8c8d;\r\n}\r\n\r\n.success-message {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: #27ae60;\r\n  color: white;\r\n  padding: 15px 20px;\r\n  border-radius: 5px;\r\n  z-index: 1001;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.2);\r\n  animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAmDA,OAAAA,aAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,aAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAD,YAAA;MACA;QACA,MAAAE,QAAA,SAAAC,KAAA,CAAAd,WAAA,CAAAC,aAAA,CAAAc,cAAA;QACA,KAAAF,QAAA,CAAAG,EAAA,YAAAC,KAAA;QACA,KAAAZ,MAAA,SAAAQ,QAAA,CAAAK,IAAA;MACA,SAAAC,GAAA;QACA,KAAAZ,KAAA;QACAa,OAAA,CAAAb,KAAA,2BAAAY,GAAA;MACA;QACA,KAAAb,OAAA;MACA;IACA;IAEAe,WAAAC,UAAA;MACA,WAAAC,IAAA,CAAAD,UAAA,EAAAE,kBAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEAC,iBAAAC,KAAA;MACA,KAAApB,aAAA,GAAAoB,KAAA;MACA,KAAArB,SAAA;MACA,KAAAsB,SAAA;IACA;IAEAC,WAAA;MACA,KAAAvB,SAAA;MACA,KAAAC,aAAA;MACA,KAAAqB,SAAA;IACA;IAEAA,UAAA;MACA,KAAAE,gBAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;IAEAC,cAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,UAAA;IACA;IAEA,MAAAC,mBAAA;MACA,UAAAH,aAAA,MAAAR,gBAAA,CAAAG,aAAA;QACAS,KAAA;QACA;MACA;MAEA,KAAAC,UAAA;MACA;QACA,MAAAC,eAAA,GAAA9C,WAAA,CAAAC,aAAA,CAAA8C,cAAA,MAAAtC,aAAA,CAAAuC,EAAA;QACA5B,OAAA,CAAA6B,GAAA,gCAAAH,eAAA;QAEA,MAAAjC,QAAA,SAAAC,KAAA,CAAAgC,eAAA;UACAI,MAAA;UACAC,OAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,MAAAtB,gBAAA;QACA;QAEAZ,OAAA,CAAA6B,GAAA,qBAAApC,QAAA,CAAA0C,MAAA;QACAnC,OAAA,CAAA6B,GAAA,kBAAApC,QAAA,CAAA2C,GAAA;QAEA,KAAA3C,QAAA,CAAAG,EAAA;UACA;UACA,IAAAH,QAAA,CAAA2C,GAAA,CAAAC,QAAA,kBAAAX,eAAA,CAAAW,QAAA;YACA,UAAAxC,KAAA;UACA;UAEA,MAAAyC,SAAA,SAAA7C,QAAA,CAAAK,IAAA,GAAAyC,KAAA;YAAAC,OAAA;UAAA;UACA,UAAA3C,KAAA,CAAAyC,SAAA,CAAAE,OAAA,uCAAA/C,QAAA,CAAA0C,MAAA;QACA;QAEA,MAAA1C,QAAA,CAAAK,IAAA;QACA,KAAA2C,cAAA,uCAAApD,aAAA,CAAAP,IAAA;QACA,KAAA6B,UAAA;QACA,KAAApB,WAAA;;QAEAmD,UAAA;UACA,KAAAD,cAAA;QACA;MAEA,SAAA1C,GAAA;QACAC,OAAA,CAAAb,KAAA,wBAAAY,GAAA;;QAEA;QACA,IAAAA,GAAA,CAAAyC,OAAA,CAAAH,QAAA;UACAb,KAAA;QACA,WAAAzB,GAAA,CAAAyC,OAAA,CAAAH,QAAA;UACAb,KAAA;QACA;UACAA,KAAA,yBAAAzB,GAAA,CAAAyC,OAAA;QACA;MACA;QACA,KAAAf,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}