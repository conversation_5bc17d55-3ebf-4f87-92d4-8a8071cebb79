{"ast": null, "code": "// API Configuration\nexport const API_BASE_URL = \"https://quiz.vilor.com\";\n\n// Build API URL helper\nexport const buildApiUrl = endpoint => {\n  return `${API_BASE_URL}${endpoint}`;\n};\n\n// API Endpoints\nexport const API_ENDPOINTS = {\n  EVENTS_LISTING: '/api/events/listing',\n  // Note: The registration endpoint might not exist yet on the server\n  // You may need to check with your backend team about the correct endpoint\n  EVENT_REGISTER: eventId => `/api/events/${eventId}/register`\n};", "map": {"version": 3, "names": ["API_BASE_URL", "buildApiUrl", "endpoint", "API_ENDPOINTS", "EVENTS_LISTING", "EVENT_REGISTER", "eventId"], "sources": ["D:/sem6/myself/vue/my-vue-project1/src/config/api.js"], "sourcesContent": ["// API Configuration\r\nexport const API_BASE_URL = \"https://quiz.vilor.com\"\r\n\r\n// Build API URL helper\r\nexport const buildApiUrl = (endpoint) => {\r\n  return `${API_BASE_URL}${endpoint}`\r\n}\r\n\r\n// API Endpoints\r\nexport const API_ENDPOINTS = {\r\n  EVENTS_LISTING: '/api/events/listing',\r\n  // Note: The registration endpoint might not exist yet on the server\r\n  // You may need to check with your backend team about the correct endpoint\r\n  EVENT_REGISTER: (eventId) => `/api/events/${eventId}/register`\r\n}"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,GAAG,wBAAwB;;AAEpD;AACA,OAAO,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EACvC,OAAO,GAAGF,YAAY,GAAGE,QAAQ,EAAE;AACrC,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,cAAc,EAAE,qBAAqB;EACrC;EACA;EACAC,cAAc,EAAGC,OAAO,IAAK,eAAeA,OAAO;AACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}