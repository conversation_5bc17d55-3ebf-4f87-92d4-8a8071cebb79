{"ast": null, "code": "import EventCalendar from './EventCalendar.vue';\nexport default {\n  name: 'EventBooking',\n  components: {\n    EventCalendar\n  },\n  data() {\n    return {\n      events: [],\n      loading: true,\n      error: null,\n      showModal: false,\n      selectedEvent: null,\n      submitting: false,\n      successMessage: '',\n      registrationForm: {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      }\n    };\n  },\n  mounted() {\n    this.fetchEvents();\n  },\n  methods: {\n    async fetchEvents() {\n      try {\n        const response = await fetch('https://quiz.vilor.com/api/events/listing');\n        if (!response.ok) throw new Error('Failed to fetch events');\n        this.events = await response.json();\n      } catch (err) {\n        this.error = 'Failed to load events. Please try again later.';\n        console.error('Error fetching events:', err);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    openRegistration(event) {\n      this.selectedEvent = event;\n      this.showModal = true;\n      this.resetForm();\n    },\n    closeModal() {\n      this.showModal = false;\n      this.selectedEvent = null;\n      this.resetForm();\n    },\n    resetForm() {\n      this.registrationForm = {\n        userFullName: '',\n        email: '',\n        contactNumber: '',\n        nric: '',\n        age: '',\n        country: '',\n        gender: ''\n      };\n    },\n    validatePhone(phone) {\n      return phone.startsWith('60');\n    },\n    async submitRegistration() {\n      if (!this.validatePhone(this.registrationForm.contactNumber)) {\n        alert('Contact number must start with 60');\n        return;\n      }\n      this.submitting = true;\n      try {\n        const response = await fetch(`/api/events/${this.selectedEvent.id}/register`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.registrationForm)\n        });\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.message || 'Registration failed');\n        }\n        await response.json();\n        this.successMessage = `Successfully registered for ${this.selectedEvent.name}!`;\n        this.closeModal();\n        this.fetchEvents(); // Refresh events to update remaining slots\n\n        setTimeout(() => {\n          this.successMessage = '';\n        }, 5000);\n      } catch (err) {\n        alert(`Registration failed: ${err.message}`);\n        console.error('Registration error:', err);\n      } finally {\n        this.submitting = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["EventCalendar", "name", "components", "data", "events", "loading", "error", "showModal", "selectedEvent", "submitting", "successMessage", "registrationForm", "userFullName", "email", "contactNumber", "nric", "age", "country", "gender", "mounted", "fetchEvents", "methods", "response", "fetch", "ok", "Error", "json", "err", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "openRegistration", "event", "resetForm", "closeModal", "validatePhone", "phone", "startsWith", "submitRegistration", "alert", "id", "method", "headers", "body", "JSON", "stringify", "errorData", "message", "setTimeout"], "sources": ["src/components/EventBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"event-booking\">\r\n    <h1>Event Booking System</h1>\r\n    \r\n    <!-- Calendar Component -->\r\n    <EventCalendar :events=\"events\" />\r\n    \r\n    <!-- Loading state -->\r\n    <div v-if=\"loading\" class=\"loading\">Loading events...</div>\r\n    \r\n    <!-- Error state -->\r\n    <div v-if=\"error\" class=\"error\">{{ error }}</div>\r\n    \r\n    <!-- Events list -->\r\n    <div v-if=\"!loading && !error\" class=\"events-container\">\r\n      <h2>All Events</h2>\r\n      <div class=\"events-grid\">\r\n        <div v-for=\"event in events\" :key=\"event.id\" class=\"event-card\">\r\n          <img :src=\"event.img\" :alt=\"event.name\" class=\"event-image\" />\r\n          <div class=\"event-content\">\r\n            <h3>{{ event.name }}</h3>\r\n            <p class=\"event-description\">{{ event.description }}</p>\r\n            <p class=\"event-date\">{{ formatDate(event.date) }}</p>\r\n            <p class=\"event-fee\">Fee: ${{ event.fee }}</p>\r\n            <p class=\"event-capacity\">Capacity: {{ event.capacity }}</p>\r\n            <p class=\"event-slots\">Remaining slots: {{ event.remainingSlots }}</p>\r\n            \r\n            <button \r\n              v-if=\"event.remainingSlots > 0\" \r\n              @click=\"openRegistration(event)\"\r\n              class=\"register-btn\"\r\n            >\r\n              Register Now\r\n            </button>\r\n            <p v-else class=\"no-slots\">Sorry, this event is fully booked!</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Registration Modal -->\r\n    <div v-if=\"showModal\" class=\"modal-overlay\" @click=\"closeModal\">\r\n      <div class=\"modal\" @click.stop>\r\n        <h3>Register for {{ selectedEvent.name }}</h3>\r\n        <p class=\"event-fee-modal\">Registration Fee: ${{ selectedEvent.fee }}</p>\r\n        <form @submit.prevent=\"submitRegistration\">\r\n          <div class=\"form-group\">\r\n            <label for=\"userFullName\">Full Name *</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"userFullName\"\r\n              v-model=\"registrationForm.userFullName\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"email\">Email *</label>\r\n            <input\r\n              type=\"email\"\r\n              id=\"email\"\r\n              v-model=\"registrationForm.email\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"contactNumber\">Contact Number *</label>\r\n            <input\r\n              type=\"tel\"\r\n              id=\"contactNumber\"\r\n              v-model=\"registrationForm.contactNumber\"\r\n              placeholder=\"60xxxxxxxxx\"\r\n              required\r\n            />\r\n            <small>Must start with 60</small>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"nric\">NRIC *</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"nric\"\r\n              v-model=\"registrationForm.nric\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"age\">Age *</label>\r\n            <input\r\n              type=\"number\"\r\n              id=\"age\"\r\n              v-model=\"registrationForm.age\"\r\n              min=\"1\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"country\">Country *</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"country\"\r\n              v-model=\"registrationForm.country\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"gender\">Gender *</label>\r\n            <select\r\n              id=\"gender\"\r\n              v-model=\"registrationForm.gender\"\r\n              required\r\n            >\r\n              <option value=\"\">Select Gender</option>\r\n              <option value=\"male\">Male</option>\r\n              <option value=\"female\">Female</option>\r\n              <option value=\"other\">Other</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"form-actions\">\r\n            <button type=\"submit\" :disabled=\"submitting\">\r\n              {{ submitting ? 'Registering...' : 'Register' }}\r\n            </button>\r\n            <button type=\"button\" @click=\"closeModal\">Cancel</button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Success Message -->\r\n    <div v-if=\"successMessage\" class=\"success-message\">\r\n      {{ successMessage }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport EventCalendar from './EventCalendar.vue'\r\n\r\nexport default {\r\n  name: 'EventBooking',\r\n  components: {\r\n    EventCalendar\r\n  },\r\n  data() {\r\n    return {\r\n      events: [],\r\n      \r\n      loading: true,\r\n      error: null,\r\n      showModal: false,\r\n      selectedEvent: null,\r\n      submitting: false,\r\n      successMessage: '',\r\n      registrationForm: {\r\n        userFullName: '',\r\n        email: '',\r\n        contactNumber: '',\r\n        nric: '',\r\n        age: '',\r\n        country: '',\r\n        gender: ''\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchEvents()\r\n  },\r\n  methods: {\r\n    async fetchEvents() {\r\n      try {\r\n        const response = await fetch('https://quiz.vilor.com/api/events/listing')\r\n        if (!response.ok) throw new Error('Failed to fetch events')\r\n        this.events = await response.json()\r\n      } catch (err) {\r\n        this.error = 'Failed to load events. Please try again later.'\r\n        console.error('Error fetching events:', err)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n      })\r\n    },\r\n    \r\n    openRegistration(event) {\r\n      this.selectedEvent = event\r\n      this.showModal = true\r\n      this.resetForm()\r\n    },\r\n    \r\n    closeModal() {\r\n      this.showModal = false\r\n      this.selectedEvent = null\r\n      this.resetForm()\r\n    },\r\n    \r\n    resetForm() {\r\n      this.registrationForm = {\r\n        userFullName: '',\r\n        email: '',\r\n        contactNumber: '',\r\n        nric: '',\r\n        age: '',\r\n        country: '',\r\n        gender: ''\r\n      }\r\n    },\r\n    \r\n    validatePhone(phone) {\r\n      return phone.startsWith('60')\r\n    },\r\n\r\n    async submitRegistration() {\r\n      if (!this.validatePhone(this.registrationForm.contactNumber)) {\r\n        alert('Contact number must start with 60')\r\n        return\r\n      }\r\n\r\n      this.submitting = true\r\n      try {\r\n        const response = await fetch(`/api/events/${this.selectedEvent.id}/register`, {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json'\r\n          },\r\n          body: JSON.stringify(this.registrationForm)\r\n        })\r\n\r\n        if (!response.ok) {\r\n          const errorData = await response.json()\r\n          throw new Error(errorData.message || 'Registration failed')\r\n        }\r\n\r\n        await response.json()\r\n        this.successMessage = `Successfully registered for ${this.selectedEvent.name}!`\r\n        this.closeModal()\r\n        this.fetchEvents() // Refresh events to update remaining slots\r\n\r\n        setTimeout(() => {\r\n          this.successMessage = ''\r\n        }, 5000)\r\n\r\n      } catch (err) {\r\n        alert(`Registration failed: ${err.message}`)\r\n        console.error('Registration error:', err)\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.event-booking {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.events-container {\r\n  margin-top: 30px;\r\n}\r\n\r\n.events-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.event-card {\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  transition: transform 0.2s;\r\n  background: white;\r\n}\r\n\r\n.event-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n}\r\n\r\n.event-content {\r\n  padding: 20px;\r\n}\r\n\r\n.event-content h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.event-description {\r\n  color: #666;\r\n  margin: 10px 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.event-date {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-fee {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-capacity {\r\n  color: #7f8c8d;\r\n  margin: 5px 0;\r\n}\r\n\r\n.event-slots {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n  margin: 10px 0;\r\n}\r\n\r\n.register-btn {\r\n  background: #3498db;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  width: 100%;\r\n  transition: background 0.2s;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: #2980b9;\r\n}\r\n\r\n.no-slots {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  padding: 12px;\r\n  background: #fdf2f2;\r\n  border-radius: 5px;\r\n  margin: 0;\r\n}\r\n\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0,0,0,0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal {\r\n  background: white;\r\n  padding: 30px;\r\n  border-radius: 8px;\r\n  width: 90%;\r\n  max-width: 500px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.event-fee-modal {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.form-group input {\r\n  width: 100%;\r\n  padding: 12px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  font-size: 16px;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.form-group input:focus {\r\n  outline: none;\r\n  border-color: #3498db;\r\n}\r\n\r\n.form-group small {\r\n  color: #666;\r\n  font-size: 12px;\r\n  margin-top: 5px;\r\n  display: block;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n  margin-top: 25px;\r\n}\r\n\r\n.form-actions button {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: background 0.2s;\r\n}\r\n\r\n.form-actions button[type=\"submit\"] {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.form-actions button[type=\"submit\"]:hover {\r\n  background: #229954;\r\n}\r\n\r\n.form-actions button[type=\"submit\"]:disabled {\r\n  background: #95a5a6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.form-actions button[type=\"button\"] {\r\n  background: #95a5a6;\r\n  color: white;\r\n}\r\n\r\n.form-actions button[type=\"button\"]:hover {\r\n  background: #7f8c8d;\r\n}\r\n\r\n.success-message {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  background: #27ae60;\r\n  color: white;\r\n  padding: 15px 20px;\r\n  border-radius: 5px;\r\n  z-index: 1001;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.2);\r\n  animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA6IA,OAAAA,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,MAAA;MAEAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,cAAA;MACAC,gBAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAD,YAAA;MACA;QACA,MAAAE,QAAA,SAAAC,KAAA;QACA,KAAAD,QAAA,CAAAE,EAAA,YAAAC,KAAA;QACA,KAAArB,MAAA,SAAAkB,QAAA,CAAAI,IAAA;MACA,SAAAC,GAAA;QACA,KAAArB,KAAA;QACAsB,OAAA,CAAAtB,KAAA,2BAAAqB,GAAA;MACA;QACA,KAAAtB,OAAA;MACA;IACA;IAEAwB,WAAAC,UAAA;MACA,WAAAC,IAAA,CAAAD,UAAA,EAAAE,kBAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEAC,iBAAAC,KAAA;MACA,KAAA7B,aAAA,GAAA6B,KAAA;MACA,KAAA9B,SAAA;MACA,KAAA+B,SAAA;IACA;IAEAC,WAAA;MACA,KAAAhC,SAAA;MACA,KAAAC,aAAA;MACA,KAAA8B,SAAA;IACA;IAEAA,UAAA;MACA,KAAA3B,gBAAA;QACAC,YAAA;QACAC,KAAA;QACAC,aAAA;QACAC,IAAA;QACAC,GAAA;QACAC,OAAA;QACAC,MAAA;MACA;IACA;IAEAsB,cAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,UAAA;IACA;IAEA,MAAAC,mBAAA;MACA,UAAAH,aAAA,MAAA7B,gBAAA,CAAAG,aAAA;QACA8B,KAAA;QACA;MACA;MAEA,KAAAnC,UAAA;MACA;QACA,MAAAa,QAAA,SAAAC,KAAA,qBAAAf,aAAA,CAAAqC,EAAA;UACAC,MAAA;UACAC,OAAA;YACA;UACA;UACAC,IAAA,EAAAC,IAAA,CAAAC,SAAA,MAAAvC,gBAAA;QACA;QAEA,KAAAW,QAAA,CAAAE,EAAA;UACA,MAAA2B,SAAA,SAAA7B,QAAA,CAAAI,IAAA;UACA,UAAAD,KAAA,CAAA0B,SAAA,CAAAC,OAAA;QACA;QAEA,MAAA9B,QAAA,CAAAI,IAAA;QACA,KAAAhB,cAAA,uCAAAF,aAAA,CAAAP,IAAA;QACA,KAAAsC,UAAA;QACA,KAAAnB,WAAA;;QAEAiC,UAAA;UACA,KAAA3C,cAAA;QACA;MAEA,SAAAiB,GAAA;QACAiB,KAAA,yBAAAjB,GAAA,CAAAyB,OAAA;QACAxB,OAAA,CAAAtB,KAAA,wBAAAqB,GAAA;MACA;QACA,KAAAlB,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}