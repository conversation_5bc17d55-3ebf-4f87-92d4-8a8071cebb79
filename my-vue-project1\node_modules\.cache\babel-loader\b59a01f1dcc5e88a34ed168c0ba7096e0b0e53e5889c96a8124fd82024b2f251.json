{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport EventCalendar from './EventCalendar.vue';\nimport EventRegistrationForm from './EventRegistrationForm.vue';\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js';\nexport default {\n  name: 'EventBooking',\n  components: {\n    EventCalendar,\n    EventRegistrationForm\n  },\n  data() {\n    return {\n      events: [],\n      loading: true,\n      error: null,\n      showModal: false,\n      selectedEvent: null,\n      searchQuery: '',\n      sortBy: 'date'\n    };\n  },\n  computed: {\n    totalSlots() {\n      return this.events.reduce((total, event) => total + event.capacity, 0);\n    },\n    availableSlots() {\n      return this.events.reduce((total, event) => total + event.remainingSlots, 0);\n    },\n    filteredAndSortedEvents() {\n      let filtered = this.events;\n\n      // Filter by search query\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(event => event.name.toLowerCase().includes(query) || event.description.toLowerCase().includes(query));\n      }\n\n      // Sort events\n      return filtered.sort((a, b) => {\n        switch (this.sortBy) {\n          case 'name':\n            return a.name.localeCompare(b.name);\n          case 'fee':\n            return a.fee - b.fee;\n          case 'slots':\n            return b.remainingSlots - a.remainingSlots;\n          case 'date':\n          default:\n            return new Date(a.date) - new Date(b.date);\n        }\n      });\n    }\n  },\n  mounted() {\n    this.fetchEvents();\n  },\n  methods: {\n    async fetchEvents() {\n      try {\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING));\n        if (!response.ok) throw new Error('Failed to fetch events');\n        this.events = await response.json();\n      } catch (err) {\n        this.error = 'Failed to load events. Please try again later.';\n        console.error('Error fetching events:', err);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    openRegistration(event) {\n      this.selectedEvent = event;\n      this.showModal = true;\n      this.resetForm();\n    },\n    closeModal() {\n      this.showModal = false;\n      this.selectedEvent = null;\n    },\n    handleRegistrationSuccess(event) {\n      // Update the event's remaining slots\n      if (event && event.remainingSlots > 0) {\n        event.remainingSlots -= 1;\n      }\n\n      // Refresh events to get latest data\n      this.fetchEvents();\n\n      // Close the modal\n      this.closeModal();\n    }\n  }\n};", "map": {"version": 3, "names": ["EventCalendar", "EventRegistrationForm", "buildApiUrl", "API_ENDPOINTS", "name", "components", "data", "events", "loading", "error", "showModal", "selectedEvent", "searchQuery", "sortBy", "computed", "totalSlots", "reduce", "total", "event", "capacity", "availableSlots", "remainingSlots", "filteredAndSortedEvents", "filtered", "query", "toLowerCase", "filter", "includes", "description", "sort", "a", "b", "localeCompare", "fee", "Date", "date", "mounted", "fetchEvents", "methods", "response", "fetch", "EVENTS_LISTING", "ok", "Error", "json", "err", "console", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "openRegistration", "resetForm", "closeModal", "handleRegistrationSuccess"], "sources": ["src/components/EventBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"event-booking\">\r\n    <!-- Hero Section -->\r\n    <div class=\"hero-section\">\r\n      <div class=\"hero-content\">\r\n        <h1 class=\"hero-title\">\r\n          <span class=\"gradient-text\">Event Booking</span>\r\n          <span class=\"hero-subtitle\">System</span>\r\n        </h1>\r\n        <p class=\"hero-description\">\r\n          Discover amazing events and book your spot with ease. Browse through our calendar or explore all available events below.\r\n        </p>\r\n        <div class=\"hero-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ events.length }}</span>\r\n            <span class=\"stat-label\">Events Available</span>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ totalSlots }}</span>\r\n            <span class=\"stat-label\">Total Slots</span>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ availableSlots }}</span>\r\n            <span class=\"stat-label\">Available Slots</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Main Content Area -->\r\n    <div class=\"main-content\">\r\n      <!-- Loading state -->\r\n      <div v-if=\"loading\" class=\"loading-container\">\r\n        <div class=\"loading-spinner\"></div>\r\n        <p class=\"loading-text\">Loading amazing events...</p>\r\n      </div>\r\n\r\n      <!-- Error state -->\r\n      <div v-if=\"error\" class=\"error-container\">\r\n        <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n        </svg>\r\n        <h3>Oops! Something went wrong</h3>\r\n        <p>{{ error }}</p>\r\n        <button @click=\"fetchEvents\" class=\"retry-btn\">\r\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <polyline points=\"23 4 23 10 17 10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <path d=\"M20.49 15a9 9 0 1 1-2.12-9.36L23 10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          </svg>\r\n          Try Again\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Content Layout with Sidebar -->\r\n      <div v-if=\"!loading && !error\" class=\"content-layout\">\r\n        <!-- Main Events Section -->\r\n        <div class=\"events-main\">\r\n          <div class=\"section-header\">\r\n            <h2 class=\"section-title\">\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path d=\"M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                <path d=\"M21 2L16 7L13 4\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n              </svg>\r\n              All Events\r\n            </h2>\r\n            <p class=\"section-description\">Browse all available events and secure your spot</p>\r\n          </div>\r\n\r\n          <!-- Filter and Sort Controls -->\r\n          <div class=\"controls-bar\">\r\n            <div class=\"search-box\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                v-model=\"searchQuery\"\r\n                class=\"search-input\"\r\n              />\r\n            </div>\r\n            <div class=\"filter-controls\">\r\n              <select v-model=\"sortBy\" class=\"sort-select\">\r\n                <option value=\"date\">Sort by Date</option>\r\n                <option value=\"name\">Sort by Name</option>\r\n                <option value=\"fee\">Sort by Fee</option>\r\n                <option value=\"slots\">Sort by Available Slots</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"events-grid\">\r\n            <div v-for=\"event in filteredAndSortedEvents\" :key=\"event.id\" class=\"event-card\">\r\n              <div class=\"event-image-container\">\r\n                <img :src=\"event.img\" :alt=\"event.name\" class=\"event-image\" />\r\n                <div class=\"event-badge\" :class=\"{ 'badge-full': event.remainingSlots === 0, 'badge-low': event.remainingSlots <= 5 && event.remainingSlots > 0 }\">\r\n                  {{ event.remainingSlots === 0 ? 'FULL' : event.remainingSlots <= 5 ? 'FILLING FAST' : 'AVAILABLE' }}\r\n                </div>\r\n              </div>\r\n              <div class=\"event-content\">\r\n                <div class=\"event-header\">\r\n                  <h3 class=\"event-title\">{{ event.name }}</h3>\r\n                  <div class=\"event-price\">${{ event.fee }}</div>\r\n                </div>\r\n                <p class=\"event-description\">{{ event.description }}</p>\r\n\r\n                <div class=\"event-details\">\r\n                  <div class=\"detail-item\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                      <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                      <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                      <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                    </svg>\r\n                    <span>{{ formatDate(event.date) }}</span>\r\n                  </div>\r\n                  <div class=\"detail-item\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7ZM23 21V19C23 17.9391 22.5786 16.9217 21.8284 16.1716C21.0783 15.4214 20.0609 15 19 15H17M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55332C18.7122 5.25592 19.0078 6.11872 19.0078 7.005C19.0078 7.89128 18.7122 8.75408 18.1676 9.45668C17.623 10.1593 16.8604 10.6597 16 10.88\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                    </svg>\r\n                    <span>{{ event.capacity }} capacity</span>\r\n                  </div>\r\n                  <div class=\"detail-item\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M22 12H18L15 21L9 3L6 12H2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                    </svg>\r\n                    <span class=\"slots-indicator\" :class=\"{ 'slots-low': event.remainingSlots <= 5, 'slots-full': event.remainingSlots === 0 }\">\r\n                      {{ event.remainingSlots }} slots left\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"event-actions\">\r\n                  <button\r\n                    v-if=\"event.remainingSlots > 0\"\r\n                    @click=\"openRegistration(event)\"\r\n                    class=\"register-btn primary\"\r\n                  >\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path d=\"M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M12.5 7.5C12.5 9.98528 10.4853 12 8 12C5.51472 12 3.5 9.98528 3.5 7.5C3.5 5.01472 5.51472 3 8 3C10.4853 3 12.5 5.01472 12.5 7.5ZM20 8V14M23 11H17\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                    </svg>\r\n                    Register Now\r\n                  </button>\r\n                  <div v-else class=\"no-slots\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                    </svg>\r\n                    Event Full\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Empty State -->\r\n          <div v-if=\"filteredAndSortedEvents.length === 0\" class=\"empty-state\">\r\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n              <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            </svg>\r\n            <h3>No events found</h3>\r\n            <p>Try adjusting your search criteria or check back later for new events.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Calendar Sidebar -->\r\n        <div class=\"calendar-sidebar\">\r\n          <div class=\"sidebar-header\">\r\n            <h3 class=\"sidebar-title\">\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n              </svg>\r\n              Event Calendar\r\n            </h3>\r\n            <p class=\"sidebar-description\">Click on dates to view events</p>\r\n          </div>\r\n          <div class=\"calendar-wrapper\">\r\n            <EventCalendar :events=\"events\" @registration-success=\"handleRegistrationSuccess\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Registration Form Component -->\r\n    <EventRegistrationForm\r\n      :show=\"showModal\"\r\n      :event=\"selectedEvent || {}\"\r\n      @close=\"closeModal\"\r\n      @registration-success=\"handleRegistrationSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport EventCalendar from './EventCalendar.vue'\r\nimport EventRegistrationForm from './EventRegistrationForm.vue'\r\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js'\r\n\r\nexport default {\r\n  name: 'EventBooking',\r\n  components: {\r\n    EventCalendar,\r\n    EventRegistrationForm\r\n  },\r\n  data() {\r\n    return {\r\n      events: [],\r\n      loading: true,\r\n      error: null,\r\n      showModal: false,\r\n      selectedEvent: null,\r\n      searchQuery: '',\r\n      sortBy: 'date'\r\n    }\r\n  },\r\n  computed: {\r\n    totalSlots() {\r\n      return this.events.reduce((total, event) => total + event.capacity, 0)\r\n    },\r\n    availableSlots() {\r\n      return this.events.reduce((total, event) => total + event.remainingSlots, 0)\r\n    },\r\n    filteredAndSortedEvents() {\r\n      let filtered = this.events\r\n\r\n      // Filter by search query\r\n      if (this.searchQuery) {\r\n        const query = this.searchQuery.toLowerCase()\r\n        filtered = filtered.filter(event =>\r\n          event.name.toLowerCase().includes(query) ||\r\n          event.description.toLowerCase().includes(query)\r\n        )\r\n      }\r\n\r\n      // Sort events\r\n      return filtered.sort((a, b) => {\r\n        switch (this.sortBy) {\r\n          case 'name':\r\n            return a.name.localeCompare(b.name)\r\n          case 'fee':\r\n            return a.fee - b.fee\r\n          case 'slots':\r\n            return b.remainingSlots - a.remainingSlots\r\n          case 'date':\r\n          default:\r\n            return new Date(a.date) - new Date(b.date)\r\n        }\r\n      })\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchEvents()\r\n  },\r\n  methods: {\r\n    async fetchEvents() {\r\n      try {\r\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING))\r\n        if (!response.ok) throw new Error('Failed to fetch events')\r\n        this.events = await response.json()\r\n      } catch (err) {\r\n        this.error = 'Failed to load events. Please try again later.'\r\n        console.error('Error fetching events:', err)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n      })\r\n    },\r\n    \r\n    openRegistration(event) {\r\n      this.selectedEvent = event\r\n      this.showModal = true\r\n      this.resetForm()\r\n    },\r\n    \r\n    closeModal() {\r\n      this.showModal = false\r\n      this.selectedEvent = null\r\n    },\r\n\r\n    handleRegistrationSuccess(event) {\r\n      // Update the event's remaining slots\r\n      if (event && event.remainingSlots > 0) {\r\n        event.remainingSlots -= 1\r\n      }\r\n\r\n      // Refresh events to get latest data\r\n      this.fetchEvents()\r\n\r\n      // Close the modal\r\n      this.closeModal()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* Global Styles */\r\n.event-booking {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n/* Hero Section */\r\n.hero-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  padding: 80px 20px;\r\n  text-align: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.hero-title {\r\n  font-size: 4rem;\r\n  font-weight: 800;\r\n  margin: 0 0 20px 0;\r\n  line-height: 1.1;\r\n}\r\n\r\n.gradient-text {\r\n  background: linear-gradient(45deg, #fff, #e0e7ff);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.hero-subtitle {\r\n  display: block;\r\n  font-size: 2.5rem;\r\n  font-weight: 300;\r\n  opacity: 0.9;\r\n}\r\n\r\n.hero-description {\r\n  font-size: 1.25rem;\r\n  margin: 0 0 40px 0;\r\n  opacity: 0.9;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.hero-stats {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 60px;\r\n  margin-top: 40px;\r\n}\r\n\r\n.stat-item {\r\n  text-align: center;\r\n}\r\n\r\n.stat-number {\r\n  display: block;\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n  display: block;\r\n  font-size: 0.875rem;\r\n  opacity: 0.8;\r\n  margin-top: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n/* Main Content Layout */\r\n.main-content {\r\n  background: white;\r\n  margin: 0;\r\n  padding: 80px 20px;\r\n}\r\n\r\n.content-layout {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: 1fr 400px;\r\n  gap: 40px;\r\n  align-items: start;\r\n}\r\n\r\n.events-main {\r\n  min-width: 0; /* Prevents grid overflow */\r\n}\r\n\r\n/* Calendar Sidebar */\r\n.calendar-sidebar {\r\n  background: #f8fafc;\r\n  border-radius: 16px;\r\n  padding: 24px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\r\n  border: 1px solid #e5e7eb;\r\n  position: sticky;\r\n  top: 20px;\r\n  max-height: calc(100vh - 40px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n}\r\n\r\n.sidebar-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.sidebar-title svg {\r\n  color: #3b82f6;\r\n}\r\n\r\n.sidebar-description {\r\n  font-size: 0.875rem;\r\n  color: #6b7280;\r\n  margin: 0;\r\n}\r\n\r\n.calendar-wrapper .calendar-container {\r\n  margin: 0;\r\n  padding: 0;\r\n  background: transparent;\r\n  box-shadow: none;\r\n}\r\n\r\n.section-header {\r\n  max-width: 1200px;\r\n  margin: 0 auto 60px auto;\r\n  text-align: center;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0 0 16px 0;\r\n}\r\n\r\n.section-title svg {\r\n  color: #3b82f6;\r\n}\r\n\r\n.section-description {\r\n  font-size: 1.125rem;\r\n  color: #6b7280;\r\n  margin: 0;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n/* Loading and Error States */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 20px;\r\n  background: white;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 48px;\r\n  height: 48px;\r\n  border: 4px solid #e5e7eb;\r\n  border-top: 4px solid #3b82f6;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 1.125rem;\r\n  color: #6b7280;\r\n  margin: 0;\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 20px;\r\n  background: white;\r\n  text-align: center;\r\n}\r\n\r\n.error-container svg {\r\n  color: #ef4444;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.error-container h3 {\r\n  font-size: 1.5rem;\r\n  color: #1f2937;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.error-container p {\r\n  color: #6b7280;\r\n  margin: 0 0 24px 0;\r\n}\r\n\r\n.retry-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: #3b82f6;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.retry-btn:hover {\r\n  background: #2563eb;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* Controls Bar */\r\n.controls-bar {\r\n  max-width: 1200px;\r\n  margin: 0 auto 40px auto;\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  flex: 1;\r\n  max-width: 400px;\r\n}\r\n\r\n.search-box svg {\r\n  position: absolute;\r\n  left: 16px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6b7280;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 12px 16px 12px 48px;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 12px;\r\n  font-size: 16px;\r\n  transition: all 0.2s;\r\n  background: white;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.sort-select {\r\n  padding: 12px 16px;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 12px;\r\n  font-size: 16px;\r\n  background: white;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.sort-select:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n}\r\n\r\n/* Events Grid */\r\n.events-grid {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.event-card {\r\n  background: white;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.event-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.event-image-container {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 240px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.event-card:hover .event-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.event-badge {\r\n  position: absolute;\r\n  top: 16px;\r\n  right: 16px;\r\n  background: #10b981;\r\n  color: white;\r\n  padding: 6px 12px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.event-badge.badge-low {\r\n  background: #f59e0b;\r\n}\r\n\r\n.event-badge.badge-full {\r\n  background: #ef4444;\r\n}\r\n\r\n.event-content {\r\n  padding: 24px;\r\n}\r\n\r\n.event-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.event-title {\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0;\r\n  flex: 1;\r\n  line-height: 1.3;\r\n}\r\n\r\n.event-price {\r\n  background: #3b82f6;\r\n  color: white;\r\n  padding: 8px 16px;\r\n  border-radius: 20px;\r\n  font-weight: 700;\r\n  font-size: 1.125rem;\r\n  margin-left: 16px;\r\n}\r\n\r\n.event-description {\r\n  color: #6b7280;\r\n  margin: 0 0 20px 0;\r\n  line-height: 1.6;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.event-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #6b7280;\r\n  font-size: 14px;\r\n}\r\n\r\n.detail-item svg {\r\n  color: #9ca3af;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.slots-indicator {\r\n  font-weight: 600;\r\n}\r\n\r\n.slots-indicator.slots-low {\r\n  color: #f59e0b;\r\n}\r\n\r\n.slots-indicator.slots-full {\r\n  color: #ef4444;\r\n}\r\n\r\n.event-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.register-btn.primary {\r\n  background: #3b82f6;\r\n  color: white;\r\n  border: none;\r\n  padding: 14px 28px;\r\n  border-radius: 12px;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 100%;\r\n  justify-content: center;\r\n}\r\n\r\n.register-btn.primary:hover {\r\n  background: #2563eb;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.no-slots {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  color: #ef4444;\r\n  font-weight: 600;\r\n  padding: 14px 28px;\r\n  background: #fef2f2;\r\n  border: 2px solid #fecaca;\r\n  border-radius: 12px;\r\n  width: 100%;\r\n}\r\n\r\n/* Empty State */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 80px 20px;\r\n  max-width: 500px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.empty-state svg {\r\n  color: #9ca3af;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.empty-state h3 {\r\n  font-size: 1.5rem;\r\n  color: #1f2937;\r\n  margin: 0 0 12px 0;\r\n}\r\n\r\n.empty-state p {\r\n  color: #6b7280;\r\n  margin: 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .hero-title {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .hero-subtitle {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .hero-stats {\r\n    gap: 30px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .stat-number {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 2rem;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .controls-bar {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .search-box {\r\n    max-width: none;\r\n  }\r\n\r\n  .events-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n  }\r\n\r\n  .event-header {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .event-price {\r\n    margin-left: 0;\r\n    align-self: flex-start;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero-section {\r\n    padding: 60px 16px;\r\n  }\r\n\r\n  .calendar-section,\r\n  .events-section {\r\n    padding: 60px 16px;\r\n  }\r\n\r\n  .hero-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .hero-subtitle {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .hero-stats {\r\n    gap: 20px;\r\n  }\r\n\r\n  .stat-number {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .event-content {\r\n    padding: 20px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;AA0MA,OAAAA,aAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,aAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,WAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,YAAAR,MAAA,CAAAS,MAAA,EAAAC,KAAA,EAAAC,KAAA,KAAAD,KAAA,GAAAC,KAAA,CAAAC,QAAA;IACA;IACAC,eAAA;MACA,YAAAb,MAAA,CAAAS,MAAA,EAAAC,KAAA,EAAAC,KAAA,KAAAD,KAAA,GAAAC,KAAA,CAAAG,cAAA;IACA;IACAC,wBAAA;MACA,IAAAC,QAAA,QAAAhB,MAAA;;MAEA;MACA,SAAAK,WAAA;QACA,MAAAY,KAAA,QAAAZ,WAAA,CAAAa,WAAA;QACAF,QAAA,GAAAA,QAAA,CAAAG,MAAA,CAAAR,KAAA,IACAA,KAAA,CAAAd,IAAA,CAAAqB,WAAA,GAAAE,QAAA,CAAAH,KAAA,KACAN,KAAA,CAAAU,WAAA,CAAAH,WAAA,GAAAE,QAAA,CAAAH,KAAA,CACA;MACA;;MAEA;MACA,OAAAD,QAAA,CAAAM,IAAA,EAAAC,CAAA,EAAAC,CAAA;QACA,aAAAlB,MAAA;UACA;YACA,OAAAiB,CAAA,CAAA1B,IAAA,CAAA4B,aAAA,CAAAD,CAAA,CAAA3B,IAAA;UACA;YACA,OAAA0B,CAAA,CAAAG,GAAA,GAAAF,CAAA,CAAAE,GAAA;UACA;YACA,OAAAF,CAAA,CAAAV,cAAA,GAAAS,CAAA,CAAAT,cAAA;UACA;UACA;YACA,WAAAa,IAAA,CAAAJ,CAAA,CAAAK,IAAA,QAAAD,IAAA,CAAAH,CAAA,CAAAI,IAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAD,YAAA;MACA;QACA,MAAAE,QAAA,SAAAC,KAAA,CAAAtC,WAAA,CAAAC,aAAA,CAAAsC,cAAA;QACA,KAAAF,QAAA,CAAAG,EAAA,YAAAC,KAAA;QACA,KAAApC,MAAA,SAAAgC,QAAA,CAAAK,IAAA;MACA,SAAAC,GAAA;QACA,KAAApC,KAAA;QACAqC,OAAA,CAAArC,KAAA,2BAAAoC,GAAA;MACA;QACA,KAAArC,OAAA;MACA;IACA;IAEAuC,WAAAC,UAAA;MACA,WAAAd,IAAA,CAAAc,UAAA,EAAAC,kBAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEAC,iBAAAnC,KAAA;MACA,KAAAP,aAAA,GAAAO,KAAA;MACA,KAAAR,SAAA;MACA,KAAA4C,SAAA;IACA;IAEAC,WAAA;MACA,KAAA7C,SAAA;MACA,KAAAC,aAAA;IACA;IAEA6C,0BAAAtC,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAG,cAAA;QACAH,KAAA,CAAAG,cAAA;MACA;;MAEA;MACA,KAAAgB,WAAA;;MAEA;MACA,KAAAkB,UAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}