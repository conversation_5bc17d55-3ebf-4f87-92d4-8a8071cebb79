{"ast": null, "code": "import EventCalendar from './EventCalendar.vue';\nimport EventRegistrationForm from './EventRegistrationForm.vue';\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js';\nexport default {\n  name: 'EventBooking',\n  components: {\n    EventCalendar,\n    EventRegistrationForm\n  },\n  data() {\n    return {\n      events: [],\n      loading: true,\n      error: null,\n      showModal: false,\n      selectedEvent: null\n    };\n  },\n  mounted() {\n    this.fetchEvents();\n  },\n  methods: {\n    async fetchEvents() {\n      try {\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING));\n        if (!response.ok) throw new Error('Failed to fetch events');\n        this.events = await response.json();\n      } catch (err) {\n        this.error = 'Failed to load events. Please try again later.';\n        console.error('Error fetching events:', err);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDate(dateString) {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    openRegistration(event) {\n      this.selectedEvent = event;\n      this.showModal = true;\n      this.resetForm();\n    },\n    closeModal() {\n      this.showModal = false;\n      this.selectedEvent = null;\n    },\n    handleRegistrationSuccess(event) {\n      // Update the event's remaining slots\n      if (event && event.remainingSlots > 0) {\n        event.remainingSlots -= 1;\n      }\n\n      // Refresh events to get latest data\n      this.fetchEvents();\n\n      // Close the modal\n      this.closeModal();\n    }\n  }\n};", "map": {"version": 3, "names": ["EventCalendar", "EventRegistrationForm", "buildApiUrl", "API_ENDPOINTS", "name", "components", "data", "events", "loading", "error", "showModal", "selectedEvent", "mounted", "fetchEvents", "methods", "response", "fetch", "EVENTS_LISTING", "ok", "Error", "json", "err", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "openRegistration", "event", "resetForm", "closeModal", "handleRegistrationSuccess", "remainingSlots"], "sources": ["src/components/EventBooking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"event-booking\">\r\n    <!-- Hero Section -->\r\n    <div class=\"hero-section\">\r\n      <div class=\"hero-content\">\r\n        <h1 class=\"hero-title\">\r\n          <span class=\"gradient-text\">Event Booking</span>\r\n          <span class=\"hero-subtitle\">System</span>\r\n        </h1>\r\n        <p class=\"hero-description\">\r\n          Discover amazing events and book your spot with ease. Browse through our calendar or explore all available events below.\r\n        </p>\r\n        <div class=\"hero-stats\">\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ events.length }}</span>\r\n            <span class=\"stat-label\">Events Available</span>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ totalSlots }}</span>\r\n            <span class=\"stat-label\">Total Slots</span>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <span class=\"stat-number\">{{ availableSlots }}</span>\r\n            <span class=\"stat-label\">Available Slots</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Calendar Section -->\r\n    <div class=\"calendar-section\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          </svg>\r\n          Event Calendar\r\n        </h2>\r\n        <p class=\"section-description\">Click on any date with events to see details and register</p>\r\n      </div>\r\n      <EventCalendar :events=\"events\" @registration-success=\"handleRegistrationSuccess\" />\r\n    </div>\r\n\r\n    <!-- Loading state -->\r\n    <div v-if=\"loading\" class=\"loading-container\">\r\n      <div class=\"loading-spinner\"></div>\r\n      <p class=\"loading-text\">Loading amazing events...</p>\r\n    </div>\r\n\r\n    <!-- Error state -->\r\n    <div v-if=\"error\" class=\"error-container\">\r\n      <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n        <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n        <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n      </svg>\r\n      <h3>Oops! Something went wrong</h3>\r\n      <p>{{ error }}</p>\r\n      <button @click=\"fetchEvents\" class=\"retry-btn\">\r\n        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <polyline points=\"23 4 23 10 17 10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          <path d=\"M20.49 15a9 9 0 1 1-2.12-9.36L23 10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n        </svg>\r\n        Try Again\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Events list -->\r\n    <div v-if=\"!loading && !error\" class=\"events-section\">\r\n      <div class=\"section-header\">\r\n        <h2 class=\"section-title\">\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <path d=\"M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <path d=\"M21 2L16 7L13 4\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          </svg>\r\n          All Events\r\n        </h2>\r\n        <p class=\"section-description\">Browse all available events and secure your spot</p>\r\n      </div>\r\n\r\n      <!-- Filter and Sort Controls -->\r\n      <div class=\"controls-bar\">\r\n        <div class=\"search-box\">\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n            <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          </svg>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search events...\"\r\n            v-model=\"searchQuery\"\r\n            class=\"search-input\"\r\n          />\r\n        </div>\r\n        <div class=\"filter-controls\">\r\n          <select v-model=\"sortBy\" class=\"sort-select\">\r\n            <option value=\"date\">Sort by Date</option>\r\n            <option value=\"name\">Sort by Name</option>\r\n            <option value=\"fee\">Sort by Fee</option>\r\n            <option value=\"slots\">Sort by Available Slots</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"events-grid\">\r\n        <div v-for=\"event in filteredAndSortedEvents\" :key=\"event.id\" class=\"event-card\">\r\n          <div class=\"event-image-container\">\r\n            <img :src=\"event.img\" :alt=\"event.name\" class=\"event-image\" />\r\n            <div class=\"event-badge\" :class=\"{ 'badge-full': event.remainingSlots === 0, 'badge-low': event.remainingSlots <= 5 && event.remainingSlots > 0 }\">\r\n              {{ event.remainingSlots === 0 ? 'FULL' : event.remainingSlots <= 5 ? 'FILLING FAST' : 'AVAILABLE' }}\r\n            </div>\r\n          </div>\r\n          <div class=\"event-content\">\r\n            <div class=\"event-header\">\r\n              <h3 class=\"event-title\">{{ event.name }}</h3>\r\n              <div class=\"event-price\">${{ event.fee }}</div>\r\n            </div>\r\n            <p class=\"event-description\">{{ event.description }}</p>\r\n\r\n            <div class=\"event-details\">\r\n              <div class=\"detail-item\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <rect x=\"3\" y=\"4\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                  <line x1=\"16\" y1=\"2\" x2=\"16\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                  <line x1=\"8\" y1=\"2\" x2=\"8\" y2=\"6\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                  <line x1=\"3\" y1=\"10\" x2=\"21\" y2=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                </svg>\r\n                <span>{{ formatDate(event.date) }}</span>\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M13 7C13 9.20914 11.2091 11 9 11C6.79086 11 5 9.20914 5 7C5 4.79086 6.79086 3 9 3C11.2091 3 13 4.79086 13 7ZM23 21V19C23 17.9391 22.5786 16.9217 21.8284 16.1716C21.0783 15.4214 20.0609 15 19 15H17M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55332C18.7122 5.25592 19.0078 6.11872 19.0078 7.005C19.0078 7.89128 18.7122 8.75408 18.1676 9.45668C17.623 10.1593 16.8604 10.6597 16 10.88\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                </svg>\r\n                <span>{{ event.capacity }} capacity</span>\r\n              </div>\r\n              <div class=\"detail-item\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M22 12H18L15 21L9 3L6 12H2\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                </svg>\r\n                <span class=\"slots-indicator\" :class=\"{ 'slots-low': event.remainingSlots <= 5, 'slots-full': event.remainingSlots === 0 }\">\r\n                  {{ event.remainingSlots }} slots left\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"event-actions\">\r\n              <button\r\n                v-if=\"event.remainingSlots > 0\"\r\n                @click=\"openRegistration(event)\"\r\n                class=\"register-btn primary\"\r\n              >\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path d=\"M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M12.5 7.5C12.5 9.98528 10.4853 12 8 12C5.51472 12 3.5 9.98528 3.5 7.5C3.5 5.01472 5.51472 3 8 3C10.4853 3 12.5 5.01472 12.5 7.5ZM20 8V14M23 11H17\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                </svg>\r\n                Register Now\r\n              </button>\r\n              <div v-else class=\"no-slots\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                  <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                  <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n                </svg>\r\n                Event Full\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Empty State -->\r\n      <div v-if=\"filteredAndSortedEvents.length === 0\" class=\"empty-state\">\r\n        <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n          <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" stroke-width=\"2\"/>\r\n        </svg>\r\n        <h3>No events found</h3>\r\n        <p>Try adjusting your search criteria or check back later for new events.</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Registration Form Component -->\r\n    <EventRegistrationForm\r\n      :show=\"showModal\"\r\n      :event=\"selectedEvent || {}\"\r\n      @close=\"closeModal\"\r\n      @registration-success=\"handleRegistrationSuccess\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport EventCalendar from './EventCalendar.vue'\r\nimport EventRegistrationForm from './EventRegistrationForm.vue'\r\nimport { buildApiUrl, API_ENDPOINTS } from '../config/api.js'\r\n\r\nexport default {\r\n  name: 'EventBooking',\r\n  components: {\r\n    EventCalendar,\r\n    EventRegistrationForm\r\n  },\r\n  data() {\r\n    return {\r\n      events: [],\r\n      loading: true,\r\n      error: null,\r\n      showModal: false,\r\n      selectedEvent: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchEvents()\r\n  },\r\n  methods: {\r\n    async fetchEvents() {\r\n      try {\r\n        const response = await fetch(buildApiUrl(API_ENDPOINTS.EVENTS_LISTING))\r\n        if (!response.ok) throw new Error('Failed to fetch events')\r\n        this.events = await response.json()\r\n      } catch (err) {\r\n        this.error = 'Failed to load events. Please try again later.'\r\n        console.error('Error fetching events:', err)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    formatDate(dateString) {\r\n      return new Date(dateString).toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n      })\r\n    },\r\n    \r\n    openRegistration(event) {\r\n      this.selectedEvent = event\r\n      this.showModal = true\r\n      this.resetForm()\r\n    },\r\n    \r\n    closeModal() {\r\n      this.showModal = false\r\n      this.selectedEvent = null\r\n    },\r\n\r\n    handleRegistrationSuccess(event) {\r\n      // Update the event's remaining slots\r\n      if (event && event.remainingSlots > 0) {\r\n        event.remainingSlots -= 1\r\n      }\r\n\r\n      // Refresh events to get latest data\r\n      this.fetchEvents()\r\n\r\n      // Close the modal\r\n      this.closeModal()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.event-booking {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error {\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 18px;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.events-container {\r\n  margin-top: 30px;\r\n}\r\n\r\n.events-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.event-card {\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n  transition: transform 0.2s;\r\n  background: white;\r\n}\r\n\r\n.event-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\r\n}\r\n\r\n.event-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n}\r\n\r\n.event-content {\r\n  padding: 20px;\r\n}\r\n\r\n.event-content h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #2c3e50;\r\n}\r\n\r\n.event-description {\r\n  color: #666;\r\n  margin: 10px 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.event-date {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-fee {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  font-size: 18px;\r\n  margin: 8px 0;\r\n}\r\n\r\n.event-capacity {\r\n  color: #7f8c8d;\r\n  margin: 5px 0;\r\n}\r\n\r\n.event-slots {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n  margin: 10px 0;\r\n}\r\n\r\n.register-btn {\r\n  background: #3498db;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  width: 100%;\r\n  transition: background 0.2s;\r\n}\r\n\r\n.register-btn:hover {\r\n  background: #2980b9;\r\n}\r\n\r\n.no-slots {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  padding: 12px;\r\n  background: #fdf2f2;\r\n  border-radius: 5px;\r\n  margin: 0;\r\n}\r\n\r\n\r\n</style>\r\n"], "mappings": "AAkMA,OAAAA,aAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,WAAA,EAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,aAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,MAAAD,YAAA;MACA;QACA,MAAAE,QAAA,SAAAC,KAAA,CAAAd,WAAA,CAAAC,aAAA,CAAAc,cAAA;QACA,KAAAF,QAAA,CAAAG,EAAA,YAAAC,KAAA;QACA,KAAAZ,MAAA,SAAAQ,QAAA,CAAAK,IAAA;MACA,SAAAC,GAAA;QACA,KAAAZ,KAAA;QACAa,OAAA,CAAAb,KAAA,2BAAAY,GAAA;MACA;QACA,KAAAb,OAAA;MACA;IACA;IAEAe,WAAAC,UAAA;MACA,WAAAC,IAAA,CAAAD,UAAA,EAAAE,kBAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEAC,iBAAAC,KAAA;MACA,KAAApB,aAAA,GAAAoB,KAAA;MACA,KAAArB,SAAA;MACA,KAAAsB,SAAA;IACA;IAEAC,WAAA;MACA,KAAAvB,SAAA;MACA,KAAAC,aAAA;IACA;IAEAuB,0BAAAH,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAI,cAAA;QACAJ,KAAA,CAAAI,cAAA;MACA;;MAEA;MACA,KAAAtB,WAAA;;MAEA;MACA,KAAAoB,UAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}